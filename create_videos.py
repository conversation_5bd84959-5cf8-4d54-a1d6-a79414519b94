import os
import cv2
import glob
from pathlib import Path
import re

def natural_sort_key(s):
    """用于自然排序的key函数"""
    return [int(text) if text.isdigit() else text.lower()
            for text in re.split('([0-9]+)', str(s))]

def create_video_from_images(image_dir, output_dir):
    """从图片序列创建视频"""
    # 获取所有PNG文件并按自然顺序排序
    images = sorted(glob.glob(os.path.join(image_dir, '*.png')), key=natural_sort_key)
    
    if not images:
        print(f"No images found in {image_dir}")
        return
    
    # 读取第一张图片来获取尺寸
    frame = cv2.imread(images[0])
    height, width = frame.shape[:2]
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取文件夹名称作为视频名称
    video_name = os.path.basename(os.path.dirname(image_dir))
    output_path = os.path.join(output_dir, f"{video_name}.mp4")
    
    # 创建视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, 10.0, (width, height))
    
    # 写入每一帧
    for image_path in images:
        frame = cv2.imread(image_path)
        out.write(frame)
    
    # 释放资源
    out.release()
    print(f"Created video: {output_path}")

def main():
    # 设置基础目录
    base_dir = "results/exp_eval_goatbench-2025-03-21"
    output_base_dir = "results/exp_eval_goatbench-2025-03-21-videos"
    
    # 创建输出目录
    os.makedirs(output_base_dir, exist_ok=True)
    
    # 找到所有以ep_0结尾的文件夹
    ep_0_dirs = glob.glob(os.path.join(base_dir, "*_ep_0"))
    
    for ep_0_dir in ep_0_dirs:
        frontier_video_dir = os.path.join(ep_0_dir, "frontier_video")
        if os.path.exists(frontier_video_dir):
            print(f"Processing {frontier_video_dir}")
            create_video_from_images(frontier_video_dir, output_base_dir)

if __name__ == "__main__":
    main() 