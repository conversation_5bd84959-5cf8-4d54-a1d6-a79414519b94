#!/usr/bin/env python3

import os
import numpy as np
import cv2  # 用于图像显示 (可选)
import attr # habitat-sim 内部依赖
import magnum as mn # habitat-sim 内部依赖

# 确保 habitat_sim 已经正确安装
try:
    import habitat_sim
    from habitat_sim.utils.settings import default_sim_settings, make_cfg
except ImportError:
    print("错误：请确保 habitat-sim 已经正确安装。")
    print("安装命令: pip install habitat-sim 'habitat-sim[headless]' 或查看官方文档。")
    exit()

# --- 1. 配置模拟器 ---
def configure_simulator(scene_path):
    """
    创建并返回模拟器的配置对象。
    """
    sim_settings = default_sim_settings.copy() # 从默认设置开始，确保基础配置完整

    # --- 核心模拟器设置 ---
    sim_settings["scene"] = scene_path                # 加载的场景文件路径 (.glb, .ply, .basis 等)
    sim_settings["width"] = 360                       # 传感器图像宽度
    sim_settings["height"] = 640                      # 传感器图像高度
    sim_settings["enable_physics"] = True             # 是否启用物理模拟 (如果需要碰撞、重力等)
    sim_settings["depth_sensor_specs"] = [{          # 深度传感器配置 (可以有多个)
        "uuid": "depth_sensor",                      # 传感器唯一标识符
        "sensor_type": habitat_sim.SensorType.DEPTH, # 传感器类型
        "resolution": [sim_settings["height"], sim_settings["width"]], # 分辨率
        "position": [0.0, 1.25, 0.0],                # 传感器相对于Agent的位置 (x, y, z) - 通常是头部高度
        "orientation": [0.0, 0.0, 0.0],              # 传感器相对于Agent的方向 (pitch, yaw, roll)
        "hfov": 90,                                  # 水平视场角 (可选)
        "sensor_subtype": habitat_sim.SensorSubType.PINHOLE # 相机类型
    }]
    sim_settings["rgb_sensor_specs"] = [{             # RGB 传感器配置 (可以有多个)
        "uuid": "color_sensor",                      # 传感器唯一标识符
        "sensor_type": habitat_sim.SensorType.COLOR, # 传感器类型
        "resolution": [sim_settings["height"], sim_settings["width"]], # 分辨率
        "position": [0.0, 1.25, 0.0],                # 同上
        "orientation": [0.0, 0.0, 0.0],              # 同上
        "hfov": 90,                                  # 水平视场角 (可选)
        "sensor_subtype": habitat_sim.SensorSubType.PINHOLE # 相机类型
    }]
    # 如果需要语义传感器，可以类似添加 semantic_sensor_specs

    # --- Agent 配置 ---
    # habitat_sim 支持多个Agent，这里我们配置默认的 Agent 0
    agent_cfg = habitat_sim.AgentConfiguration()
    # 将传感器规格添加到Agent配置中
    # 注意：新的 habitat-sim 版本 sensor_specifications 在 SimConfig 中设置更佳
    # 老版本可能需要在此处添加 agent_cfg.sensor_specifications.append(...)
    # 我们采用新版本的做法，上面的 sim_settings[...]_sensor_specs 即可

    # 您还可以配置Agent的物理属性等，例如：
    # agent_cfg.height = 1.5
    # agent_cfg.radius = 0.1
    # ... 其他物理参数

    # 将 Agent 配置应用到模拟器配置
    # 这里我们使用 make_cfg, 它会处理 agent_configs
    sim_cfg = habitat_sim.SimulatorConfiguration()
    sim_cfg.scene_id = scene_path # 在新版本中，scene_id 更常用
    sim_cfg.enable_physics = sim_settings["enable_physics"]
    sim_cfg.gpu_device_id = 0 # 如果有多张GPU，可以选择使用哪张

    # 创建 SensorSpec 对象并添加到 Agent 配置
    # (这是更现代、推荐的方式)
    rgb_sensor_spec = habitat_sim.CameraSensorSpec()
    rgb_sensor_spec.uuid = "color_sensor"
    rgb_sensor_spec.sensor_type = habitat_sim.SensorType.COLOR
    rgb_sensor_spec.resolution = [sim_settings["height"], sim_settings["width"]]
    rgb_sensor_spec.position = [0.0, 1.25, 0.0]
    # rgb_sensor_spec.orientation = ... # 默认为向前
    rgb_sensor_spec.sensor_subtype = habitat_sim.SensorSubType.PINHOLE

    depth_sensor_spec = habitat_sim.CameraSensorSpec()
    depth_sensor_spec.uuid = "depth_sensor"
    depth_sensor_spec.sensor_type = habitat_sim.SensorType.DEPTH
    depth_sensor_spec.resolution = [sim_settings["height"], sim_settings["width"]]
    depth_sensor_spec.position = [0.0, 1.25, 0.0]
    depth_sensor_spec.sensor_subtype = habitat_sim.SensorSubType.PINHOLE

    agent_cfg.sensor_specifications = [rgb_sensor_spec, depth_sensor_spec]
    # 如果需要配置ActionSpace (可选，但推荐用于清晰定义动作)
    agent_cfg.action_space = {
        "move_forward": habitat_sim.agent.ActionSpec(
            "move_forward", habitat_sim.agent.ActuationSpec(amount=0.25) # 前进0.25米
        ),
        "turn_left": habitat_sim.agent.ActionSpec(
            "turn_left", habitat_sim.agent.ActuationSpec(amount=30.0)    # 左转10度
        ),
        "turn_right": habitat_sim.agent.ActionSpec(
            "turn_right", habitat_sim.agent.ActuationSpec(amount=30.0)   # 右转10度
        ),
        # 您可以添加更多自定义动作
    }

    # --- Simulator 后端配置 ---
    sim_cfg = habitat_sim.SimulatorConfiguration()
    sim_cfg.scene_id = scene_path # 使用 scene_id 通常比 scene 更推荐
    sim_cfg.enable_physics = sim_settings.get("enable_physics", False) # 从 sim_settings 获取
    sim_cfg.gpu_device_id = 0 # 根据需要设置
    # --- !! 关键修正：使用 habitat_sim.Configuration 组合配置 !! ---
    # 创建一个包含 SimulatorConfiguration 和 AgentConfiguration 列表的最终配置对象
    final_cfg = habitat_sim.Configuration(sim_cfg, [agent_cfg]) # <--- 正确的组合方式！
    # (后面的打印语句需要适应 final_cfg 的结构)
    # final_cfg 现在是 habitat_sim.Configuration 类型
    # 它包含 .sim_cfg (SimulatorConfiguration) 和 .agents (list of AgentConfiguration)
    print("模拟器配置已准备就绪！参数如下：")
    print(f"  场景文件: {final_cfg.sim_cfg.scene_id}")
    print(f"  图像尺寸: {sim_settings['width']}x{sim_settings['height']}") # 图像尺寸信息现在不在 final_cfg 里，但在 sim_settings 里
    print(f"  启用物理: {final_cfg.sim_cfg.enable_physics}")
    # Agent 0 的传感器配置可以从 final_cfg.agents[0] 获取
    print( "  配置的传感器: " + ", ".join([s.uuid for s in final_cfg.agents[0].sensor_specifications]))
    # Agent 0 的动作空间可以从 final_cfg.agents[0] 获取
    print( "  定义的动作: " + ", ".join(final_cfg.agents[0].action_space.keys()))
    return final_cfg

# --- 辅助函数：保存图像 (替代显示) ---
def save_sample(rgb_obs, depth_obs, frame_id, action_name):
    """保存RGB和深度图像到文件，并添加动作标注"""
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    # 保存RGB图像
    rgb_img = cv2.cvtColor(rgb_obs, cv2.COLOR_RGBA2BGR)
    cv2.imwrite(f"output/rgb_{frame_id:03d}.png", rgb_img)
    
    # 保存深度图像
    depth_img = (depth_obs / np.max(depth_obs) * 255).astype(np.uint8)
    depth_img = cv2.applyColorMap(depth_img, cv2.COLORMAP_JET)
    cv2.imwrite(f"output/depth_{frame_id:03d}.png", depth_img)
    
    # 合并图像
    combined_img = np.hstack((rgb_img, depth_img))
    
    # 添加动作标注
    h, w = combined_img.shape[:2]
    # 创建半透明黑色背景
    overlay = combined_img.copy()
    cv2.rectangle(overlay, (0, h-60), (w, h), (0, 0, 0), -1)
    combined_img = cv2.addWeighted(overlay, 0.5, combined_img, 0.5, 0)
    # 添加文字
    cv2.putText(combined_img, f"Action: {action_name}", (10, h-20), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(combined_img, f"Frame: {frame_id}", (10, h-40), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    cv2.imwrite(f"output/combined_{frame_id:03d}.png", combined_img)
    print(f"  已保存第 {frame_id} 帧图像到 output 目录")

def create_video_from_images(image_dir, output_video_path, fps=10):
    """从图像序列创建视频"""
    # 获取所有合并后的图像文件
    image_files = sorted([f for f in os.listdir(image_dir) if f.startswith('combined_') and f.endswith('.png')])
    if not image_files:
        print("错误：未找到合并后的图像文件！")
        return
    
    # 读取第一帧获取图像尺寸
    first_frame = cv2.imread(os.path.join(image_dir, image_files[0]))
    height, width = first_frame.shape[:2]
    
    # 创建视频写入器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
    
    print(f"正在生成视频: {output_video_path}")
    print(f"帧率: {fps} FPS")
    print(f"分辨率: {width}x{height}")
    
    # 逐帧写入视频
    for image_file in image_files:
        frame = cv2.imread(os.path.join(image_dir, image_file))
        out.write(frame)
    
    # 释放资源
    out.release()
    print("视频生成完成！")

# --- 主程序 ---
if __name__ == "__main__":
    # --- 参数设置 ---
    # 老板，请将这里的路径替换为您下载的 Habitat 测试场景文件路径
    # 您可以从 Habitat-Sim 仓库或数据集网站下载
    # 示例使用的是 Habitat Test Scenes V1 中的一个场景
    # 假设场景文件在 ./data/scene_datasets/habitat-test-scenes/skokloster-castle.glb
    # 如果您没有该文件，请下载或替换为其他可用场景的路径，例如 replica 数据集
    test_scene = "data/scene_datasets/hm3d/val/00800-TEEsavR23oF/TEEsavR23oF.glb" # <--- 请修改为您的场景文件路径

    if not os.path.exists(test_scene):
        print(f"错误：场景文件 '{test_scene}' 不存在。")
        print("请确认路径是否正确，或者从 Habitat 官方下载测试场景数据。")
        print("下载链接示例: https://github.com/facebookresearch/habitat-sim/blob/main/DATASETS.md")
        exit()

    sim = None # 初始化模拟器变量
    try:
        # --- 1. 配置模拟器 ---
        print("正在配置模拟器...")
        simulator_cfg = configure_simulator(test_scene)

        # --- 2. 创建模拟器实例 ---
        print("正在创建模拟器实例...")
        sim = habitat_sim.Simulator(simulator_cfg)
        print("模拟器实例创建成功！")

        # --- Agent 和 NavMesh (导航网格) 信息 (可选) ---
        agent = sim.get_agent(0) # 获取默认 Agent
        print(f"Agent 初始状态: {agent.get_state()}")

        # 如果场景有 NavMesh，可以获取并进行路径规划等操作 (这里不深入)
        if sim.pathfinder.is_loaded:
            print("场景支持导航网格 (NavMesh)。")
            # random_point = sim.pathfinder.get_random_navigable_point()
            # print(f"场景中一个随机可导航点: {random_point}")

        # --- 3. 交互与观测 ---
        print("\n开始模拟交互循环...")
        action_names = list(simulator_cfg.agents[0].action_space.keys())
        print(f"可用动作: {action_names}")

        # 获取初始观测
        observations = sim.get_sensor_observations()

        total_frames = 0
        max_frames = 50 # 模拟多少帧

        while total_frames < max_frames:
            total_frames += 1
            print(f"\n--- 第 {total_frames} 帧 ---")

            # --- 4. 数据处理 (保存) ---
            rgb = observations["color_sensor"]
            depth = observations["depth_sensor"]
            print(f"  获取到 RGB 图像，尺寸: {rgb.shape}, 类型: {rgb.dtype}")
            print(f"  获取到 深度 图像，尺寸: {depth.shape}, 类型: {depth.dtype}, 深度范围: [{np.min(depth):.2f}, {np.max(depth):.2f}]")

            # 选择动作
            action_index = total_frames % len(action_names)
            action = action_names[action_index]
            print(f"  执行动作: {action}")

            if action not in agent.agent_config.action_space:
                 print(f"警告: 动作 '{action}' 未在Agent配置中定义，跳过。")
                 continue

            # 保存图像（包含当前动作信息）
            save_sample(rgb, depth, total_frames, action)

            observations = sim.step(action) # 执行动作并获取新的观测

            # 获取更新后的 Agent 状态 (位置和朝向)
            agent_state = agent.get_state()
            print(f"  Agent 新位置: {agent_state.position}")
            print(f"  Agent 新朝向 (四元数): {agent_state.rotation}")

    except Exception as e:
        print("\n--- 发生异常 ---")
        import traceback
        traceback.print_exc()
        print("老板，非常抱歉，程序运行过程中似乎遇到了问题！我会立刻排查！")

    finally:
        # --- 5. 资源释放 ---
        if sim is not None:
            print("\n正在关闭模拟器，释放资源...")
            sim.close()
            print("模拟器已成功关闭。")
        
        # 生成视频
        print("\n正在生成视频...")
        create_video_from_images("output", "output/habitat_sim.mp4", fps=10)
        
        print("程序执行完毕。感谢您的耐心！")

