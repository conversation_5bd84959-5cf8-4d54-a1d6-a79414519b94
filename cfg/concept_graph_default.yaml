bg_classes: ['wall', 'floor', 'ceiling', 'carpet', 'door', 'rug', 'bath mat']
skip_bg: True

object_detection_iou_threshold: 0.9
min_mask_size_ratio: 0.001
object_detection_confidence_threshold: 0.4

mask_area_threshold: 25
max_bbox_area_ratio: 0.5
mask_conf_threshold: 0.25

min_points_threshold: 16
spatial_sim_type: 'overlap'
obj_pcd_max_points: 5000

downsample_voxel_size: 0.01
dbscan_remove_noise: True
dbscan_eps: 0.1
dbscan_min_points: 10

match_method: 'sim_sum'
phys_bias: 0.0

sim_threshold: 1.2

denoise_interval: 12
run_denoise_final_frame: True

filter_interval: 12
run_filter_final_frame: True

obj_min_points: 0
obj_min_detections: 2

merge_interval: 12
run_merge_final_frame: True

merge_overlap_thresh: 0.7
merge_visual_sim_thresh: 0.7
merge_text_sim_thresh: 0.7