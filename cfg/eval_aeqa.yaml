# General
seed: 77
exp_name: "exp_eval_aeqa"
output_parent_dir: "results"
scene_dataset_config_path: "data/hm3d_annotated_basis.scene_dataset_config.json"
scene_data_path: "your_hm3d_path"
questions_list_path: 'data/aeqa_questions-41.json'

concept_graph_config_path: "cfg/concept_graph_default.yaml"

# major settings
choose_every_step: true  # whether to query vlm at each step, or only after arriving at the navigation target
egocentric_views: true  # whether to add egocentric views when prompting vlm
prefiltering: true  # whether to use prefiltering (you in fact cannot turn this off, since otherwise it will exceed the context length limit)
top_k_categories: 10  # keep the top k relevant categories during prefiltering

# about detection model
yolo_model_name: yolov8x-world.pt
sam_model_name: sam_l.pt
class_set: scannet200  # use the 200-class set for yolo-world detector

# about snapshots clustering
min_detection: 1

# camera, image
camera_height: 1.5
camera_tilt_deg: -30
img_width: 1280
img_height: 1280
hfov: 120

# whether to save visualization (which is slow)
save_visualization: true

# the image size for prompting gpt-4o
prompt_h: 360
prompt_w: 360

# navigation
num_step: 50
init_clearance: 0.3
extra_view_phase_1: 2  # the number of extra views
extra_view_angle_deg_phase_1: 60  # the angle between each extra view
extra_view_phase_2: 6
extra_view_angle_deg_phase_2: 40

# about tsdf, depth map, and frontier updates
explored_depth: 1.7
tsdf_grid_size: 0.1
margin_w_ratio: 0.25
margin_h_ratio: 0.6
planner:
  eps: 1
  max_dist_from_cur_phase_1: 1  # when the target object is not found, explore the frontiers with this step length
  max_dist_from_cur_phase_2: 1  # when the target object is found, go to the target object with this step length
  final_observe_distance: 0.75  # in phase 2, find a place that is this far from the target object to observe it
  surrounding_explored_radius: 0.7

  # about frontier selection
  frontier_edge_area_min: 4
  frontier_edge_area_max: 6
  frontier_area_min: 8
  frontier_area_max: 9
  min_frontier_area: 20  # the frontier should have at least this many pixels
  max_frontier_angle_range_deg: 150  # the angle spanned by the pixels in a frontier should not be larger than this
  region_equal_threshold: 0.95

# about scene graph construction
scene_graph:
  confidence: 0.003
  nms_threshold: 0.1
  iou_threshold: 0.5
  obj_include_dist: 3.5
  target_obj_iou_threshold: 0.6