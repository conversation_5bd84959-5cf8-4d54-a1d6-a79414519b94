{"stages": {"paths": {".glb": ["example/00861-GLAQ4DNUx5U/*.basis.glb", "minival/00800-TEEsavR23oF/*.basis.glb", "minival/00802-wcojb4TFT35/*.basis.glb", "minival/00803-k1cupFYWXJ6/*.basis.glb", "minival/00808-y9hTuugGdiq/*.basis.glb", "test/00900-XRJ2muKkwAV/*.basis.glb", "test/00901-aJoC8Qw6xQ5/*.basis.glb", "test/00904-S9CUp5RsFY9/*.basis.glb", "test/00905-9v5HzFAB1cm/*.basis.glb", "test/00907-WGxjrSp71zX/*.basis.glb", "test/00909-MrKys1cv4jD/*.basis.glb", "test/00910-jE7Qzu3FyJD/*.basis.glb", "test/00911-a8h4mZT8buN/*.basis.glb", "test/00913-URu5ybF8PrH/*.basis.glb", "test/00914-TxyEAAVD4iD/*.basis.glb", "test/00915-ieavb8rbmA9/*.basis.glb", "test/00918-NQX2SZD1TMJ/*.basis.glb", "test/00921-nhtTwUrWv7v/*.basis.glb", "test/00925-4QUpJVDQ1DN/*.basis.glb", "test/00926-egVUByjgpHU/*.basis.glb", "test/00927-kucQY8TbKkz/*.basis.glb", "test/00929-azb2LDaMrGZ/*.basis.glb", "test/00939-VedHjk858Vu/*.basis.glb", "test/00940-oHcTqmvveM7/*.basis.glb", "test/00947-fyZC1Whhj9i/*.basis.glb", "test/00951-RGvKsZAErEk/*.basis.glb", "test/00952-ogAZhviACbN/*.basis.glb", "test/00955-5TxBa2qkcAh/*.basis.glb", "test/00956-oNiVkiFKkAr/*.basis.glb", "test/00957-DowRMDn4MVe/*.basis.glb", "test/00961-dU7KLdn2Kcd/*.basis.glb", "test/00963-awRQoguC7TN/*.basis.glb", "test/00967-XwXCfkthawu/*.basis.glb", "test/00970-kUmc6DP2tff/*.basis.glb", "test/00977-1cQ7WUQgXJ4/*.basis.glb", "test/00978-sZJ5zo6FrEe/*.basis.glb", "test/00981-Rp8JNfstaew/*.basis.glb", "test/00985-3ZJjFFjJKqZ/*.basis.glb", "test/00989-t9gvgFMoH4R/*.basis.glb", "test/00992-G3AqUkGnykx/*.basis.glb", "train/00006-HkseAnWCgqk/*.basis.glb", "train/00009-vLpv2VX547B/*.basis.glb", "train/00016-qk9eeNeR4vw/*.basis.glb", "train/00017-oEPjPNSPmzL/*.basis.glb", "train/00020-XYyR54sxe6b/*.basis.glb", "train/00022-gmuS7Wgsbrx/*.basis.glb", "train/00023-zepmXAdrpjR/*.basis.glb", "train/00025-ixTj1aTMup2/*.basis.glb", "train/00031-Wo6kuutE9i7/*.basis.glb", "train/00033-oPj9qMxrDEa/*.basis.glb", "train/00034-6imZUJGRUq4/*.basis.glb", "train/00035-3XYAD64HpDr/*.basis.glb", "train/00043-Jfyvj3xn2aJ/*.basis.glb", "train/00055-HxmXPBbFCkH/*.basis.glb", "train/00057-1UnKg1rAb8A/*.basis.glb", "train/00059-kJxT5qssH4H/*.basis.glb", "train/00062-ACZZiU6BXLz/*.basis.glb", "train/00064-gQgtJ9Stk5s/*.basis.glb", "train/00081-5biL7VEkByM/*.basis.glb", "train/00087-YY8rqV6L6rf/*.basis.glb", "train/00096-6HRFAUDqpTb/*.basis.glb", "train/00099-226REUyJh2K/*.basis.glb", "train/00105-xWvSkKiWQpC/*.basis.glb", "train/00108-oStKKWkQ1id/*.basis.glb", "train/00109-GTV2Y73Sn5t/*.basis.glb", "train/00135-HeSYRw7eMtG/*.basis.glb", "train/00141-iigzG1rtanx/*.basis.glb", "train/00143-5Kw4nGdqYtS/*.basis.glb", "train/00149-UuwwmrTsfBN/*.basis.glb", "train/00150-LcAd9dhvVwh/*.basis.glb", "train/00155-iLDo95ZbDJq/*.basis.glb", "train/00164-XfUxBGTFQQb/*.basis.glb", "train/00166-RaYrxWt5pR1/*.basis.glb", "train/00168-bHKTDQFJxTw/*.basis.glb", "train/00172-bB6nKqfsb1z/*.basis.glb", "train/00173-qZ4B7U6XE5Y/*.basis.glb", "train/00177-VSxVP19Cdyw/*.basis.glb", "train/00179-MVVzj944atG/*.basis.glb", "train/00188-dQrLTxHvLXU/*.basis.glb", "train/00203-VoVGtfYrpuQ/*.basis.glb", "train/00205-NEVASPhcrxR/*.basis.glb", "train/00207-FRQ75PjD278/*.basis.glb", "train/00210-j2EJhFEQGCL/*.basis.glb", "train/00217-qz3829g1Lzf/*.basis.glb", "train/00222-g8Xrdbe9fir/*.basis.glb", "train/00234-nACV8wLu1u5/*.basis.glb", "train/00238-j6fHrce9pHR/*.basis.glb", "train/00241-h6nwVLpAKQz/*.basis.glb", "train/00245-741Fdj7NLF9/*.basis.glb", "train/00250-U3oQjwTuMX8/*.basis.glb", "train/00251-wsAYBFtQaL7/*.basis.glb", "train/00254-YMNvYDhK8mB/*.basis.glb", "train/00255-NGyoyh91xXJ/*.basis.glb", "train/00256-92vYG1q49FY/*.basis.glb", "train/00258-2Pc8W48bu21/*.basis.glb", "train/00261-fK2vEV32Lag/*.basis.glb", "train/00262-1xGrZPxG1Hz/*.basis.glb", "train/00263-GGBvSFddQgs/*.basis.glb", "train/00267-gQ3xxshDiCz/*.basis.glb", "train/00269-JNiWU5TZLtt/*.basis.glb", "train/00272-kA2nG18hCAr/*.basis.glb", "train/00294-PPTLa8SkUfo/*.basis.glb", "train/00299-bdp1XNEdvmW/*.basis.glb", "train/00304-X6Pct1msZv5/*.basis.glb", "train/00307-vDfkYo5VqEQ/*.basis.glb", "train/00313-PE6kVEtrxtj/*.basis.glb", "train/00323-yHLr6bvWsVm/*.basis.glb", "train/00324-DoSbsoo4EAg/*.basis.glb", "train/00326-u9rPN5cHWBg/*.basis.glb", "train/00327-xgLmjqzoAzF/*.basis.glb", "train/00330-WhNyDTnd9g5/*.basis.glb", "train/00366-fxbzYAGkrtm/*.basis.glb", "train/00378-DqJKU7YU7dA/*.basis.glb", "train/00382-S7uMvxjBVZq/*.basis.glb", "train/00384-ceJTwFNjqCt/*.basis.glb", "train/00386-b3WpMbPFB6q/*.basis.glb", "train/00388-pcpn6mFqFCg/*.basis.glb", "train/00397-ZNanfzgCdm3/*.basis.glb", "train/00401-H8rQCnvBgo6/*.basis.glb", "train/00404-QN2dRqwd84J/*.basis.glb", "train/00407-NPHxDe6VeCc/*.basis.glb", "train/00410-v7DzfFFEpsD/*.basis.glb", "train/00414-77mMEyxhs44/*.basis.glb", "train/00417-nGhNxKrgBPb/*.basis.glb", "train/00422-8wJuSPJ9FXG/*.basis.glb", "train/00434-L5QEsaVqwrY/*.basis.glb", "train/00440-wPLokgvCnuk/*.basis.glb", "train/00444-sX9xad6ULKc/*.basis.glb", "train/00463-URjpCob8MGw/*.basis.glb", "train/00466-xAHnY3QzFUN/*.basis.glb", "train/00475-g7hUFVNac26/*.basis.glb", "train/00476-NtnvZSMK3en/*.basis.glb", "train/00487-erXNfWVjqZ8/*.basis.glb", "train/00495-CQWES1bawee/*.basis.glb", "train/00506-QVAA6zecMHu/*.basis.glb", "train/00508-4vwGX7U38Ux/*.basis.glb", "train/00525-iKFn6fzyRqs/*.basis.glb", "train/00529-W9YAR9qcuvN/*.basis.glb", "train/00534-DBBESbk4Y3k/*.basis.glb", "train/00537-oahi4u45xMf/*.basis.glb", "train/00538-3CBBjsNkhqW/*.basis.glb", "train/00539-zUG6FL9TYeR/*.basis.glb", "train/00541-FnDDfrBZPhh/*.basis.glb", "train/00546-nS8T59Aw3sf/*.basis.glb", "train/00547-9h5JJxM6E5S/*.basis.glb", "train/00557-fRZhp6vWGw7/*.basis.glb", "train/00560-gjhYih4upQ9/*.basis.glb", "train/00567-KjZrPggnHm8/*.basis.glb", "train/00569-YJDUB7hWg9h/*.basis.glb", "train/00582-TYDavTf8oyy/*.basis.glb", "train/00590-qgZhhx1MpTi/*.basis.glb", "train/00591-JptJPosx1Z6/*.basis.glb", "train/00592-CthA7sQNTPK/*.basis.glb", "train/00598-mt9H8KcxRKD/*.basis.glb", "train/00606-W16Bm4ysK8v/*.basis.glb", "train/00612-GsQBY83r3hb/*.basis.glb", "train/00619-R9fYpvCUkV7/*.basis.glb", "train/00624-ooq3SnvC79d/*.basis.glb", "train/00626-XiJhRLvpKpX/*.basis.glb", "train/00638-iePHCSf119p/*.basis.glb", "train/00643-ggNAcMh8JPT/*.basis.glb", "train/00657-TSJmdttd2GV/*.basis.glb", "train/00662-aRKASs4e8j1/*.basis.glb", "train/00664-u5atqC7vRCY/*.basis.glb", "train/00668-6YtDG3FhNvx/*.basis.glb", "train/00669-DNWbUAJYsPy/*.basis.glb", "train/00680-YmWinf3mhb5/*.basis.glb", "train/00685-ENiCjXWB6aQ/*.basis.glb", "train/00688-SgkmkWjjmDJ/*.basis.glb", "train/00696-DsEJeNPcZtE/*.basis.glb", "train/00706-YHmAkqgwe2p/*.basis.glb", "train/00707-XVSZJAtHKdi/*.basis.glb", "train/00712-HZ2iMMBsBQ9/*.basis.glb", "train/00720-8B43pG641ff/*.basis.glb", "train/00723-hWDDQnSDMXb/*.basis.glb", "train/00732-Z2DQddYp1fn/*.basis.glb", "train/00733-GtM3JtRvvvR/*.basis.glb", "train/00738-GPyDUnjwZQy/*.basis.glb", "train/00741-w8GiikYuFRk/*.basis.glb", "train/00744-1S7LAXRdDqK/*.basis.glb", "train/00745-yX5efd48dLf/*.basis.glb", "train/00746-RTV2n6fXB2w/*.basis.glb", "train/00750-E1NrAhMoqvB/*.basis.glb", "train/00757-LVgQNuK8vtv/*.basis.glb", "train/00758-HfMobPm86Xn/*.basis.glb", "val/00800-TEEsavR23oF/*.basis.glb", "val/00802-wcojb4TFT35/*.basis.glb", "val/00803-k1cupFYWXJ6/*.basis.glb", "val/00808-y9hTuugGdiq/*.basis.glb", "val/00810-CrMo8WxCyVb/*.basis.glb", "val/00813-svBbv1Pavdk/*.basis.glb", "val/00814-p53SfW6mjZe/*.basis.glb", "val/00815-h1zeeAwLh9Z/*.basis.glb", "val/00820-mL8ThkuaVTM/*.basis.glb", "val/00821-eF36g7L6Z9M/*.basis.glb", "val/00823-7MXmsvcQjpJ/*.basis.glb", "val/00824-Dd4bFSTQ8gi/*.basis.glb", "val/00827-BAbdmeyTvMZ/*.basis.glb", "val/00829-QaLdnwvtxbs/*.basis.glb", "val/00831-yr17PDCnDDW/*.basis.glb", "val/00832-qyAac8rV8Zk/*.basis.glb", "val/00835-q3zU7Yy5E5s/*.basis.glb", "val/00839-zt1RVoi7PcG/*.basis.glb", "val/00843-DYehNKdT76V/*.basis.glb", "val/00844-q5QZSEeHe5g/*.basis.glb", "val/00847-bCPU9suPUw9/*.basis.glb", "val/00848-ziup5kvtCCR/*.basis.glb", "val/00849-a8BtkwhxdRV/*.basis.glb", "val/00853-5cdEh9F2hJL/*.basis.glb", "val/00861-GLAQ4DNUx5U/*.basis.glb", "val/00862-LT9Jq6dN3Ea/*.basis.glb", "val/00869-MHPLjHsuG27/*.basis.glb", "val/00871-VBzV5z6i1WS/*.basis.glb", "val/00873-bxsVRursffK/*.basis.glb", "val/00876-mv2HUxq3B53/*.basis.glb", "val/00877-4ok3usBNeis/*.basis.glb", "val/00878-XB4GS9ShBRE/*.basis.glb", "val/00880-Nfvxx8J5NCo/*.basis.glb", "val/00890-6s7QHgap2fW/*.basis.glb", "val/00891-cvZr5TUy5C5/*.basis.glb", "val/00894-HY1NcmCgn3n/*.basis.glb"]}, "default_attributes": {"shader_type": "flat", "up": [0, 0, 1], "front": [0, 1, 0], "origin": [0, 0, 0], "semantic_descriptor_filename": "%%CONFIG_NAME_AS_ASSET_FILENAME%%.semantic.txt", "semantic_asset": "%%CONFIG_NAME_AS_ASSET_FILENAME%%.semantic.glb", "has_semantic_textures": true}}, "objects": {}, "light_setups": {}, "scene_instances": {"default_attributes": {"default_lighting": "no_lights"}, "paths": {".json": ["example/00861-GLAQ4DNUx5U/*.basis.scene_instance.json", "minival/00800-TEEsavR23oF/*.basis.scene_instance.json", "minival/00802-wcojb4TFT35/*.basis.scene_instance.json", "minival/00803-k1cupFYWXJ6/*.basis.scene_instance.json", "minival/00808-y9hTuugGdiq/*.basis.scene_instance.json", "test/00900-XRJ2muKkwAV/*.basis.scene_instance.json", "test/00901-aJoC8Qw6xQ5/*.basis.scene_instance.json", "test/00904-S9CUp5RsFY9/*.basis.scene_instance.json", "test/00905-9v5HzFAB1cm/*.basis.scene_instance.json", "test/00907-WGxjrSp71zX/*.basis.scene_instance.json", "test/00909-MrKys1cv4jD/*.basis.scene_instance.json", "test/00910-jE7Qzu3FyJD/*.basis.scene_instance.json", "test/00911-a8h4mZT8buN/*.basis.scene_instance.json", "test/00913-URu5ybF8PrH/*.basis.scene_instance.json", "test/00914-TxyEAAVD4iD/*.basis.scene_instance.json", "test/00915-ieavb8rbmA9/*.basis.scene_instance.json", "test/00918-NQX2SZD1TMJ/*.basis.scene_instance.json", "test/00921-nhtTwUrWv7v/*.basis.scene_instance.json", "test/00925-4QUpJVDQ1DN/*.basis.scene_instance.json", "test/00926-egVUByjgpHU/*.basis.scene_instance.json", "test/00927-kucQY8TbKkz/*.basis.scene_instance.json", "test/00929-azb2LDaMrGZ/*.basis.scene_instance.json", "test/00939-VedHjk858Vu/*.basis.scene_instance.json", "test/00940-oHcTqmvveM7/*.basis.scene_instance.json", "test/00947-fyZC1Whhj9i/*.basis.scene_instance.json", "test/00951-RGvKsZAErEk/*.basis.scene_instance.json", "test/00952-ogAZhviACbN/*.basis.scene_instance.json", "test/00955-5TxBa2qkcAh/*.basis.scene_instance.json", "test/00956-oNiVkiFKkAr/*.basis.scene_instance.json", "test/00957-DowRMDn4MVe/*.basis.scene_instance.json", "test/00961-dU7KLdn2Kcd/*.basis.scene_instance.json", "test/00963-awRQoguC7TN/*.basis.scene_instance.json", "test/00967-XwXCfkthawu/*.basis.scene_instance.json", "test/00970-kUmc6DP2tff/*.basis.scene_instance.json", "test/00977-1cQ7WUQgXJ4/*.basis.scene_instance.json", "test/00978-sZJ5zo6FrEe/*.basis.scene_instance.json", "test/00981-Rp8JNfstaew/*.basis.scene_instance.json", "test/00985-3ZJjFFjJKqZ/*.basis.scene_instance.json", "test/00989-t9gvgFMoH4R/*.basis.scene_instance.json", "test/00992-G3AqUkGnykx/*.basis.scene_instance.json", "train/00006-HkseAnWCgqk/*.basis.scene_instance.json", "train/00009-vLpv2VX547B/*.basis.scene_instance.json", "train/00016-qk9eeNeR4vw/*.basis.scene_instance.json", "train/00017-oEPjPNSPmzL/*.basis.scene_instance.json", "train/00020-XYyR54sxe6b/*.basis.scene_instance.json", "train/00022-gmuS7Wgsbrx/*.basis.scene_instance.json", "train/00023-zepmXAdrpjR/*.basis.scene_instance.json", "train/00025-ixTj1aTMup2/*.basis.scene_instance.json", "train/00031-Wo6kuutE9i7/*.basis.scene_instance.json", "train/00033-oPj9qMxrDEa/*.basis.scene_instance.json", "train/00034-6imZUJGRUq4/*.basis.scene_instance.json", "train/00035-3XYAD64HpDr/*.basis.scene_instance.json", "train/00043-Jfyvj3xn2aJ/*.basis.scene_instance.json", "train/00055-HxmXPBbFCkH/*.basis.scene_instance.json", "train/00057-1UnKg1rAb8A/*.basis.scene_instance.json", "train/00059-kJxT5qssH4H/*.basis.scene_instance.json", "train/00062-ACZZiU6BXLz/*.basis.scene_instance.json", "train/00064-gQgtJ9Stk5s/*.basis.scene_instance.json", "train/00081-5biL7VEkByM/*.basis.scene_instance.json", "train/00087-YY8rqV6L6rf/*.basis.scene_instance.json", "train/00096-6HRFAUDqpTb/*.basis.scene_instance.json", "train/00099-226REUyJh2K/*.basis.scene_instance.json", "train/00105-xWvSkKiWQpC/*.basis.scene_instance.json", "train/00108-oStKKWkQ1id/*.basis.scene_instance.json", "train/00109-GTV2Y73Sn5t/*.basis.scene_instance.json", "train/00135-HeSYRw7eMtG/*.basis.scene_instance.json", "train/00141-iigzG1rtanx/*.basis.scene_instance.json", "train/00143-5Kw4nGdqYtS/*.basis.scene_instance.json", "train/00149-UuwwmrTsfBN/*.basis.scene_instance.json", "train/00150-LcAd9dhvVwh/*.basis.scene_instance.json", "train/00155-iLDo95ZbDJq/*.basis.scene_instance.json", "train/00164-XfUxBGTFQQb/*.basis.scene_instance.json", "train/00166-RaYrxWt5pR1/*.basis.scene_instance.json", "train/00168-bHKTDQFJxTw/*.basis.scene_instance.json", "train/00172-bB6nKqfsb1z/*.basis.scene_instance.json", "train/00173-qZ4B7U6XE5Y/*.basis.scene_instance.json", "train/00177-VSxVP19Cdyw/*.basis.scene_instance.json", "train/00179-MVVzj944atG/*.basis.scene_instance.json", "train/00188-dQrLTxHvLXU/*.basis.scene_instance.json", "train/00203-VoVGtfYrpuQ/*.basis.scene_instance.json", "train/00205-NEVASPhcrxR/*.basis.scene_instance.json", "train/00207-FRQ75PjD278/*.basis.scene_instance.json", "train/00210-j2EJhFEQGCL/*.basis.scene_instance.json", "train/00217-qz3829g1Lzf/*.basis.scene_instance.json", "train/00222-g8Xrdbe9fir/*.basis.scene_instance.json", "train/00234-nACV8wLu1u5/*.basis.scene_instance.json", "train/00238-j6fHrce9pHR/*.basis.scene_instance.json", "train/00241-h6nwVLpAKQz/*.basis.scene_instance.json", "train/00245-741Fdj7NLF9/*.basis.scene_instance.json", "train/00250-U3oQjwTuMX8/*.basis.scene_instance.json", "train/00251-wsAYBFtQaL7/*.basis.scene_instance.json", "train/00254-YMNvYDhK8mB/*.basis.scene_instance.json", "train/00255-NGyoyh91xXJ/*.basis.scene_instance.json", "train/00256-92vYG1q49FY/*.basis.scene_instance.json", "train/00258-2Pc8W48bu21/*.basis.scene_instance.json", "train/00261-fK2vEV32Lag/*.basis.scene_instance.json", "train/00262-1xGrZPxG1Hz/*.basis.scene_instance.json", "train/00263-GGBvSFddQgs/*.basis.scene_instance.json", "train/00267-gQ3xxshDiCz/*.basis.scene_instance.json", "train/00269-JNiWU5TZLtt/*.basis.scene_instance.json", "train/00272-kA2nG18hCAr/*.basis.scene_instance.json", "train/00294-PPTLa8SkUfo/*.basis.scene_instance.json", "train/00299-bdp1XNEdvmW/*.basis.scene_instance.json", "train/00304-X6Pct1msZv5/*.basis.scene_instance.json", "train/00307-vDfkYo5VqEQ/*.basis.scene_instance.json", "train/00313-PE6kVEtrxtj/*.basis.scene_instance.json", "train/00323-yHLr6bvWsVm/*.basis.scene_instance.json", "train/00324-DoSbsoo4EAg/*.basis.scene_instance.json", "train/00326-u9rPN5cHWBg/*.basis.scene_instance.json", "train/00327-xgLmjqzoAzF/*.basis.scene_instance.json", "train/00330-WhNyDTnd9g5/*.basis.scene_instance.json", "train/00366-fxbzYAGkrtm/*.basis.scene_instance.json", "train/00378-DqJKU7YU7dA/*.basis.scene_instance.json", "train/00382-S7uMvxjBVZq/*.basis.scene_instance.json", "train/00384-ceJTwFNjqCt/*.basis.scene_instance.json", "train/00386-b3WpMbPFB6q/*.basis.scene_instance.json", "train/00388-pcpn6mFqFCg/*.basis.scene_instance.json", "train/00397-ZNanfzgCdm3/*.basis.scene_instance.json", "train/00401-H8rQCnvBgo6/*.basis.scene_instance.json", "train/00404-QN2dRqwd84J/*.basis.scene_instance.json", "train/00407-NPHxDe6VeCc/*.basis.scene_instance.json", "train/00410-v7DzfFFEpsD/*.basis.scene_instance.json", "train/00414-77mMEyxhs44/*.basis.scene_instance.json", "train/00417-nGhNxKrgBPb/*.basis.scene_instance.json", "train/00422-8wJuSPJ9FXG/*.basis.scene_instance.json", "train/00434-L5QEsaVqwrY/*.basis.scene_instance.json", "train/00440-wPLokgvCnuk/*.basis.scene_instance.json", "train/00444-sX9xad6ULKc/*.basis.scene_instance.json", "train/00463-URjpCob8MGw/*.basis.scene_instance.json", "train/00466-xAHnY3QzFUN/*.basis.scene_instance.json", "train/00475-g7hUFVNac26/*.basis.scene_instance.json", "train/00476-NtnvZSMK3en/*.basis.scene_instance.json", "train/00487-erXNfWVjqZ8/*.basis.scene_instance.json", "train/00495-CQWES1bawee/*.basis.scene_instance.json", "train/00506-QVAA6zecMHu/*.basis.scene_instance.json", "train/00508-4vwGX7U38Ux/*.basis.scene_instance.json", "train/00525-iKFn6fzyRqs/*.basis.scene_instance.json", "train/00529-W9YAR9qcuvN/*.basis.scene_instance.json", "train/00534-DBBESbk4Y3k/*.basis.scene_instance.json", "train/00537-oahi4u45xMf/*.basis.scene_instance.json", "train/00538-3CBBjsNkhqW/*.basis.scene_instance.json", "train/00539-zUG6FL9TYeR/*.basis.scene_instance.json", "train/00541-FnDDfrBZPhh/*.basis.scene_instance.json", "train/00546-nS8T59Aw3sf/*.basis.scene_instance.json", "train/00547-9h5JJxM6E5S/*.basis.scene_instance.json", "train/00557-fRZhp6vWGw7/*.basis.scene_instance.json", "train/00560-gjhYih4upQ9/*.basis.scene_instance.json", "train/00567-KjZrPggnHm8/*.basis.scene_instance.json", "train/00569-YJDUB7hWg9h/*.basis.scene_instance.json", "train/00582-TYDavTf8oyy/*.basis.scene_instance.json", "train/00590-qgZhhx1MpTi/*.basis.scene_instance.json", "train/00591-JptJPosx1Z6/*.basis.scene_instance.json", "train/00592-CthA7sQNTPK/*.basis.scene_instance.json", "train/00598-mt9H8KcxRKD/*.basis.scene_instance.json", "train/00606-W16Bm4ysK8v/*.basis.scene_instance.json", "train/00612-GsQBY83r3hb/*.basis.scene_instance.json", "train/00619-R9fYpvCUkV7/*.basis.scene_instance.json", "train/00624-ooq3SnvC79d/*.basis.scene_instance.json", "train/00626-XiJhRLvpKpX/*.basis.scene_instance.json", "train/00638-iePHCSf119p/*.basis.scene_instance.json", "train/00643-ggNAcMh8JPT/*.basis.scene_instance.json", "train/00657-TSJmdttd2GV/*.basis.scene_instance.json", "train/00662-aRKASs4e8j1/*.basis.scene_instance.json", "train/00664-u5atqC7vRCY/*.basis.scene_instance.json", "train/00668-6YtDG3FhNvx/*.basis.scene_instance.json", "train/00669-DNWbUAJYsPy/*.basis.scene_instance.json", "train/00680-YmWinf3mhb5/*.basis.scene_instance.json", "train/00685-ENiCjXWB6aQ/*.basis.scene_instance.json", "train/00688-SgkmkWjjmDJ/*.basis.scene_instance.json", "train/00696-DsEJeNPcZtE/*.basis.scene_instance.json", "train/00706-YHmAkqgwe2p/*.basis.scene_instance.json", "train/00707-XVSZJAtHKdi/*.basis.scene_instance.json", "train/00712-HZ2iMMBsBQ9/*.basis.scene_instance.json", "train/00720-8B43pG641ff/*.basis.scene_instance.json", "train/00723-hWDDQnSDMXb/*.basis.scene_instance.json", "train/00732-Z2DQddYp1fn/*.basis.scene_instance.json", "train/00733-GtM3JtRvvvR/*.basis.scene_instance.json", "train/00738-GPyDUnjwZQy/*.basis.scene_instance.json", "train/00741-w8GiikYuFRk/*.basis.scene_instance.json", "train/00744-1S7LAXRdDqK/*.basis.scene_instance.json", "train/00745-yX5efd48dLf/*.basis.scene_instance.json", "train/00746-RTV2n6fXB2w/*.basis.scene_instance.json", "train/00750-E1NrAhMoqvB/*.basis.scene_instance.json", "train/00757-LVgQNuK8vtv/*.basis.scene_instance.json", "train/00758-HfMobPm86Xn/*.basis.scene_instance.json", "val/00800-TEEsavR23oF/*.basis.scene_instance.json", "val/00802-wcojb4TFT35/*.basis.scene_instance.json", "val/00803-k1cupFYWXJ6/*.basis.scene_instance.json", "val/00808-y9hTuugGdiq/*.basis.scene_instance.json", "val/00810-CrMo8WxCyVb/*.basis.scene_instance.json", "val/00813-svBbv1Pavdk/*.basis.scene_instance.json", "val/00814-p53SfW6mjZe/*.basis.scene_instance.json", "val/00815-h1zeeAwLh9Z/*.basis.scene_instance.json", "val/00820-mL8ThkuaVTM/*.basis.scene_instance.json", "val/00821-eF36g7L6Z9M/*.basis.scene_instance.json", "val/00823-7MXmsvcQjpJ/*.basis.scene_instance.json", "val/00824-Dd4bFSTQ8gi/*.basis.scene_instance.json", "val/00827-BAbdmeyTvMZ/*.basis.scene_instance.json", "val/00829-QaLdnwvtxbs/*.basis.scene_instance.json", "val/00831-yr17PDCnDDW/*.basis.scene_instance.json", "val/00832-qyAac8rV8Zk/*.basis.scene_instance.json", "val/00835-q3zU7Yy5E5s/*.basis.scene_instance.json", "val/00839-zt1RVoi7PcG/*.basis.scene_instance.json", "val/00843-DYehNKdT76V/*.basis.scene_instance.json", "val/00844-q5QZSEeHe5g/*.basis.scene_instance.json", "val/00847-bCPU9suPUw9/*.basis.scene_instance.json", "val/00848-ziup5kvtCCR/*.basis.scene_instance.json", "val/00849-a8BtkwhxdRV/*.basis.scene_instance.json", "val/00853-5cdEh9F2hJL/*.basis.scene_instance.json", "val/00861-GLAQ4DNUx5U/*.basis.scene_instance.json", "val/00862-LT9Jq6dN3Ea/*.basis.scene_instance.json", "val/00869-MHPLjHsuG27/*.basis.scene_instance.json", "val/00871-VBzV5z6i1WS/*.basis.scene_instance.json", "val/00873-bxsVRursffK/*.basis.scene_instance.json", "val/00876-mv2HUxq3B53/*.basis.scene_instance.json", "val/00877-4ok3usBNeis/*.basis.scene_instance.json", "val/00878-XB4GS9ShBRE/*.basis.scene_instance.json", "val/00880-Nfvxx8J5NCo/*.basis.scene_instance.json", "val/00890-6s7QHgap2fW/*.basis.scene_instance.json", "val/00891-cvZr5TUy5C5/*.basis.scene_instance.json", "val/00894-HY1NcmCgn3n/*.basis.scene_instance.json"]}}}