[{"question": "What is the white object on the wall above the TV?", "answer": "Air conditioning unit", "category": "object recognition", "question_id": "f2e82760-5c3c-41b1-88b6-85921b9e7b32", "episode_history": "00826-BFRyYbPCCPE", "position": [-3.2461330890655518, 0.17934048175811768, -2.557859182357788], "rotation": [0.9077942371368408, 0.0, 0.4194158613681793, 0.0], "object_id": null, "class": null}, {"question": "Is there room on the dining table to eat?", "answer": "Yes", "category": "spatial understanding", "question_id": "79344680-6b45-4531-8789-ad0f5ef85b3b", "episode_history": "00826-BFRyYbPCCPE", "position": [-3.2461330890655518, 0.17934048175811768, -2.557859182357788], "rotation": [0.9077942371368408, 0.0, 0.4194158613681793, 0.0], "object_id": null, "class": null}, {"question": "Is the front door open?", "answer": "No", "category": "object state recognition", "question_id": "ecad68d2-a16f-4a3e-b8a1-a70ec1c5cf00", "episode_history": "00826-BFRyYbPCCPE", "position": [-3.2461330890655518, 0.17934048175811768, -2.557859182357788], "rotation": [0.9077942371368408, 0.0, 0.4194158613681793, 0.0], "object_id": null, "class": null}, {"question": "What shape are the door knobs?", "answer": "Round or spherical", "category": "attribute recognition", "question_id": "e816a1f9-af6c-4901-8d54-4ddaa2a60dc3", "episode_history": "00886-TPhiubUHKcP", "position": [-4.679103851318359, 0.16253697872161865, -4.211667060852051], "rotation": [-0.07081564515829086, 0.0, 0.9974894523620605, 0.0], "object_id": null, "class": null}, {"question": "What is to the left of the staircase?", "answer": "A storage closet", "category": "spatial understanding", "question_id": "5460114d-e885-4eae-8bdc-a273deb3df0a", "episode_history": "00886-TPhiubUHKcP", "position": [-4.679103851318359, 0.16253697872161865, -4.211667060852051], "rotation": [-0.07081564515829086, 0.0, 0.9974894523620605, 0.0], "object_id": null, "class": null}, {"question": "What color is the car?", "answer": "Blue", "category": "attribute recognition", "question_id": "a86ef102-5500-4fbf-8fae-cdbeb20a3b7b", "episode_history": "00802-wcojb4TFT35", "position": [-2.8347792625427246, -0.29074591398239136, -14.94974136352539], "rotation": [-0.724639892578125, 0.0, 0.6891277432441711, 0.0], "object_id": null, "class": null}, {"question": "What material is the floor?", "answer": "concrete", "category": "attribute recognition", "question_id": "438e8c6f-f27d-4d3d-b13d-6f41c2981c2a", "episode_history": "00802-wcojb4TFT35", "position": [-2.8347792625427246, -0.29074591398239136, -14.94974136352539], "rotation": [-0.724639892578125, 0.0, 0.6891277432441711, 0.0], "object_id": null, "class": null}, {"question": "What is on the top shelf to the right side of the garage?", "answer": "An ice cooler", "category": "spatial understanding", "question_id": "9b4a7fbb-680d-4e39-8d60-7b1e521f3108", "episode_history": "00802-wcojb4TFT35", "position": [-2.8347792625427246, -0.29074591398239136, -14.94974136352539], "rotation": [-0.724639892578125, 0.0, 0.6891277432441711, 0.0], "object_id": null, "class": null}, {"question": "What type of car is in the garage?", "answer": "A sedan", "category": "world knowledge", "question_id": "7f15d867-8ba6-47fa-9bca-9d9ae64046b7", "episode_history": "00802-wcojb4TFT35", "position": [-2.8347792625427246, -0.29074591398239136, -14.94974136352539], "rotation": [-0.724639892578125, 0.0, 0.6891277432441711, 0.0], "object_id": null, "class": null}, {"question": "Which bin should I put paper in?", "answer": "The bin with the yellow lid.", "category": "world knowledge", "question_id": "025257b6-8b7e-4f6f-aacc-1788069cbfad", "episode_history": "00802-wcojb4TFT35", "position": [-2.8347792625427246, -0.29074591398239136, -14.94974136352539], "rotation": [-0.724639892578125, 0.0, 0.6891277432441711, 0.0], "object_id": null, "class": null}, {"question": "What is the object to the left of the bed?", "answer": "A radiator", "category": "object recognition", "question_id": "23fb241e-989a-4299-a3fb-8d41f7156397", "episode_history": "00855-c5eTyR3Rxyh", "position": [3.398106098175049, 0.14745813608169556, 0.0978868380188942], "rotation": [0.5577243566513062, 0.0, 0.8300262093544006, 0.0], "object_id": null, "class": null}, {"question": "What shape is the mirror?", "answer": "An oval", "category": "attribute recognition", "question_id": "915cb310-31be-4114-846c-242fc59b581d", "episode_history": "00855-c5eTyR3Rxyh", "position": [3.398106098175049, 0.14745813608169556, 0.0978868380188942], "rotation": [0.5577243566513062, 0.0, 0.8300262093544006, 0.0], "object_id": null, "class": null}, {"question": "What color is the bed comforter?", "answer": "<PERSON>", "category": "attribute recognition", "question_id": "d7019200-5300-459e-a2c1-b54d5ec0a80b", "episode_history": "00855-c5eTyR3Rxyh", "position": [3.398106098175049, 0.14745813608169556, 0.0978868380188942], "rotation": [0.5577243566513062, 0.0, 0.8300262093544006, 0.0], "object_id": null, "class": null}, {"question": "Does the bedroom have a lot of furniture?", "answer": "No", "category": "spatial understanding", "question_id": "6be2fe87-f20c-48a2-a8fb-161362d86e2a", "episode_history": "00855-c5eTyR3Rxyh", "position": [3.398106098175049, 0.14745813608169556, 0.0978868380188942], "rotation": [0.5577243566513062, 0.0, 0.8300262093544006, 0.0], "object_id": null, "class": null}, {"question": "Where is the orange painting?", "answer": "Above the bed", "category": "object localization", "question_id": "f776a834-1e21-4442-8834-18b6f9d6cfad", "episode_history": "00855-c5eTyR3Rxyh", "extra_answers": ["in the bedroom", "on the wall next to the bed", "above the bed", "on the wall across the french doors"], "position": [3.398106098175049, 0.14745813608169556, 0.0978868380188942], "rotation": [0.5577243566513062, 0.0, 0.8300262093544006, 0.0], "object_id": null, "class": null}, {"question": "What is above the red couch?", "answer": "Paintings", "category": "object recognition", "question_id": "9a0fe947-4c0b-47b8-a1dc-414f2d555c67", "episode_history": "00875-66seV3BWPoX", "position": [-2.3507192134857178, -2.9361133575439453, 0.62546306848526], "rotation": [-0.47572049498558044, 0.0, 0.8795965313911438, 0.0], "object_id": null, "class": null}, {"question": "Is the front door open?", "answer": "No", "category": "object state recognition", "question_id": "38ce32f5-3c19-46c3-94e6-79efa00a6fbe", "episode_history": "00893-yZME6UR9dUN", "position": [-3.615100383758545, 0.2697516977787018, -3.05495023727417], "rotation": [-0.9999750256538391, 0.0, 0.007067576050758362, 0.0], "object_id": null, "class": null}, {"question": "Where can I brush my teeth?", "answer": "In the bathroom sink", "category": "functional reasoning", "question_id": "0e49111c-608d-4d02-aacb-3705bdd0ca5e", "episode_history": "00893-yZME6UR9dUN", "position": [-3.615100383758545, 0.2697516977787018, -3.05495023727417], "rotation": [-0.9999750256538391, 0.0, 0.007067576050758362, 0.0], "object_id": null, "class": null}, {"question": "Where can I set if I want to warmup?", "answer": "By the fireplace", "category": "world knowledge", "question_id": "41f53d99-4277-4dec-892e-8e52a2cc7402", "episode_history": "00893-yZME6UR9dUN", "position": [-3.615100383758545, 0.2697516977787018, -3.05495023727417], "rotation": [-0.9999750256538391, 0.0, 0.007067576050758362, 0.0], "object_id": null, "class": null}, {"question": "What is to the left of the mirror downstairs?", "answer": "Light switch", "category": "object recognition", "question_id": "10d6d14b-ef30-42b6-89d7-b79eb4ce9b5d", "episode_history": "00816-q3hn1WQ12rz", "position": [-0.9988259673118591, -2.753734827041626, -0.11362335085868835], "rotation": [0.5558836460113525, 0.0, 0.8312601447105408, 0.0], "object_id": null, "class": null}, {"question": "What is on the center of the ceiling in the living room?", "answer": "A smoke detector", "category": "spatial understanding", "question_id": "59128ef6-1338-49a8-ab06-191971bb1815", "episode_history": "00816-q3hn1WQ12rz", "position": [-0.9988259673118591, -2.753734827041626, -0.11362335085868835], "rotation": [0.5558836460113525, 0.0, 0.8312601447105408, 0.0], "object_id": null, "class": null}, {"question": "Are the ceiling lights in the living room turned on?", "answer": "Yes", "category": "object state recognition", "question_id": "d6142b7c-27e3-4aef-bca7-2cfddca328f4", "episode_history": "00816-q3hn1WQ12rz", "position": [-0.9988259673118591, -2.753734827041626, -0.11362335085868835], "rotation": [0.5558836460113525, 0.0, 0.8312601447105408, 0.0], "object_id": null, "class": null}, {"question": "Where should I put my jackets and shoes?", "answer": "In the storage closet ", "category": "functional reasoning", "question_id": "24228768-d745-4796-990f-2b5d8aeb4827", "episode_history": "00816-q3hn1WQ12rz", "position": [-0.9988259673118591, -2.753734827041626, -0.11362335085868835], "rotation": [0.5558836460113525, 0.0, 0.8312601447105408, 0.0], "object_id": null, "class": null}, {"question": "How many stories does this house have?", "answer": "Three", "category": "world knowledge", "question_id": "22c31dab-ea65-4752-b541-edcdb3c67108", "episode_history": "00816-q3hn1WQ12rz", "position": [-0.9988259673118591, -2.753734827041626, -0.11362335085868835], "rotation": [0.5558836460113525, 0.0, 0.8312601447105408, 0.0], "object_id": null, "class": null}, {"question": "Where is the blue cabinet?", "answer": "In the upstairs bedroom ", "category": "object localization", "question_id": "8548aacb-669f-4341-a21e-0426e5dc3b42", "episode_history": "00816-q3hn1WQ12rz", "extra_answers": ["in the upstairs bedroom", "in the bedroom on the second floor below the painting", "Upstairs in the room immediately after the stairs", "On the bedroom"], "position": [-0.9988259673118591, -2.753734827041626, -0.11362335085868835], "rotation": [0.5558836460113525, 0.0, 0.8312601447105408, 0.0], "object_id": null, "class": null}, {"question": "What cartoon animal is painted on the wall?", "answer": "An owl", "category": "object recognition", "question_id": "98a31a80-3f7b-416a-ba1a-fc1858523860", "episode_history": "00873-bxsVRursffK", "position": [-5.7067461013793945, 0.024676084518432617, -4.672021389007568], "rotation": [-0.9858109354972839, 0.0, 0.16785979270935059, 0.0], "object_id": null, "class": null}, {"question": "What color are the flowers on the black table downstairs?", "answer": "White", "category": "attribute recognition", "question_id": "6e4d210c-e7b0-4e71-96e9-d4f58f17b3ea", "episode_history": "00873-bxsVRursffK", "position": [-5.7067461013793945, 0.024676084518432617, -4.672021389007568], "rotation": [-0.9858109354972839, 0.0, 0.16785979270935059, 0.0], "object_id": null, "class": null}, {"question": "Who is the room with the books meant for?", "answer": "A child", "category": "world knowledge", "question_id": "8c26c6d7-4d26-4069-9829-53f01c6d0cae", "episode_history": "00873-bxsVRursffK", "position": [-5.7067461013793945, 0.024676084518432617, -4.672021389007568], "rotation": [-0.9858109354972839, 0.0, 0.16785979270935059, 0.0], "object_id": null, "class": null}, {"question": "What colors are the stripes on the wall with the books?", "answer": "Blue, Green, and Red", "category": "object localization", "question_id": "352d1df4-83c8-430c-8d6e-f8b477d7e1c1", "episode_history": "00873-bxsVRursffK", "extra_answers": ["blue, green, and red", "blue, red, and green", "blue, red and green", "red, green and blue"], "position": [-5.7067461013793945, 0.024676084518432617, -4.672021389007568], "rotation": [-0.9858109354972839, 0.0, 0.16785979270935059, 0.0], "object_id": null, "class": null}, {"question": "What color is the jacket on the clothes hangar?", "answer": "Maroon or red", "category": "attribute recognition", "question_id": "28694964-f409-42ee-b3a1-22b17c7f3408", "episode_history": "00854-SiKqEZx7Ejt", "position": [1.1471482515335083, -1.257657766342163, 5.204402446746826], "rotation": [0.3662199378013611, 0.0, 0.9305283427238464, 0.0], "object_id": null, "class": null}, {"question": "What is to the left of the dining table?", "answer": "A wine cabinet", "category": "spatial understanding", "question_id": "b9fa3fcf-34f1-4eb5-a6d1-3fb4465ade39", "episode_history": "00854-SiKqEZx7Ejt", "position": [1.1471482515335083, -1.257657766342163, 5.204402446746826], "rotation": [0.3662199378013611, 0.0, 0.9305283427238464, 0.0], "object_id": null, "class": null}, {"question": "Is the cloth hanger rack full?", "answer": "No.", "category": "object state recognition", "question_id": "853d340b-c69d-4371-894c-5e1151844b14", "episode_history": "00854-SiKqEZx7Ejt", "position": [1.1471482515335083, -1.257657766342163, 5.204402446746826], "rotation": [0.3662199378013611, 0.0, 0.9305283427238464, 0.0], "object_id": null, "class": null}, {"question": "Where can I put my jacket?", "answer": "On the cloth hanger rack", "category": "functional reasoning", "question_id": "f26d0764-cfaa-4d85-8adf-8be0a3c1864d", "episode_history": "00854-SiKqEZx7Ejt", "position": [1.1471482515335083, -1.257657766342163, 5.204402446746826], "rotation": [0.3662199378013611, 0.0, 0.9305283427238464, 0.0], "object_id": null, "class": null}, {"question": "What kind of material is the wall on the right side of the staircase?", "answer": "Exposed stone", "category": "world knowledge", "question_id": "5cc891f2-c7fd-478a-bbc0-03a4b7c66472", "episode_history": "00854-SiKqEZx7Ejt", "position": [1.1471482515335083, -1.257657766342163, 5.204402446746826], "rotation": [0.3662199378013611, 0.0, 0.9305283427238464, 0.0], "object_id": null, "class": null}, {"question": "What is the color of the walls in the bedroom?", "answer": "Light blue", "category": "attribute recognition", "question_id": "c5a16c11-e855-4abe-bfe5-33df48982386", "episode_history": "00853-5cdEh9F2hJL", "position": [-4.755180358886719, 0.29464560747146606, 4.835386276245117], "rotation": [-0.4670323133468628, 0.0, 0.8842402696609497, 0.0], "object_id": null, "class": null}, {"question": "Where is the full body mirror?", "answer": "In the bedroom by the door", "category": "object localization", "question_id": "69376f0e-ffd7-4d04-aad3-6089bacfc1d3", "episode_history": "00853-5cdEh9F2hJL", "extra_answers": ["next to the bedroom door", "just inside the bedroom", "in the bedroom", "in the bedroom right next to the door"], "position": [-4.755180358886719, 0.29464560747146606, 4.835386276245117], "rotation": [-0.4670323133468628, 0.0, 0.8842402696609497, 0.0], "object_id": null, "class": null}, {"question": "What color pattern is on the pillow on the black couch?", "answer": "A leopard skin color pattern", "category": "attribute recognition", "question_id": "4a0b1006-0209-4e6a-a0fa-dab6835b6605", "episode_history": "00892-bzCsHPLDztK", "position": [-0.15797314047813416, -0.13510484993457794, 6.968340873718262], "rotation": [-0.7889631986618042, 0.0, 0.6144405603408813, 0.0], "object_id": null, "class": null}, {"question": "Are the curtains to the patio door open?", "answer": "Yes", "category": "object state recognition", "question_id": "8985bd89-1b04-4328-869e-75c416eab90b", "episode_history": "00892-bzCsHPLDztK", "position": [-0.15797314047813416, -0.13510484993457794, 6.968340873718262], "rotation": [-0.7889631986618042, 0.0, 0.6144405603408813, 0.0], "object_id": null, "class": null}, {"question": "Is this home in the city?", "answer": "No.", "category": "world knowledge", "question_id": "0b48b97e-4a15-4181-bff3-8852f09f2f3e", "episode_history": "00892-bzCsHPLDztK", "position": [-0.15797314047813416, -0.13510484993457794, 6.968340873718262], "rotation": [-0.7889631986618042, 0.0, 0.6144405603408813, 0.0], "object_id": null, "class": null}, {"question": "Where is the bongo?", "answer": "On top of the brown shelf", "category": "object localization", "question_id": "07c4017d-db5a-447a-8086-17d9472e7100", "episode_history": "00892-bzCsHPLDztK", "extra_answers": ["On top of the bookshelf in the kitchen. ", "In the kitchen on top of the wooden shelves.", "on top of brown shelf next to the door in the living room", "on top of brown shelf next to the door in the room with TV "], "position": [-0.15797314047813416, -0.13510484993457794, 6.968340873718262], "rotation": [-0.7889631986618042, 0.0, 0.6144405603408813, 0.0], "object_id": null, "class": null}, {"question": "What animal is shown in the picture in the bedroom?", "answer": "A bird", "category": "attribute recognition", "question_id": "d36c5ac4-65b9-4979-881c-56c7d0870a50", "episode_history": "00878-XB4GS9ShBRE", "position": [-2.8504385948181152, 2.7961301803588867, -0.9476851224899292], "rotation": [-0.9700993895530701, 0.0, 0.24270813167095184, 0.0], "object_id": null, "class": null}, {"question": "Is there space on the dining table to work on my laptop?", "answer": "Yes", "category": "spatial understanding", "question_id": "16345ba0-9217-4f07-a79d-bbb965bc69a4", "episode_history": "00878-XB4GS9ShBRE", "position": [-2.8504385948181152, 2.7961301803588867, -0.9476851224899292], "rotation": [-0.9700993895530701, 0.0, 0.24270813167095184, 0.0], "object_id": null, "class": null}, {"question": "Is the bathroom shower curtain open or closed?", "answer": "Open", "category": "object state recognition", "question_id": "15ef0e88-83c5-41dd-9a1f-cf9feb3dafbb", "episode_history": "00878-XB4GS9ShBRE", "position": [-2.8504385948181152, 2.7961301803588867, -0.9476851224899292], "rotation": [-0.9700993895530701, 0.0, 0.24270813167095184, 0.0], "object_id": null, "class": null}, {"question": "Where did I put my pink shirt?", "answer": "In the bedroom closet", "category": "object localization", "question_id": "b4de266c-5361-46b7-a098-167d6ee4d5c1", "episode_history": "00878-XB4GS9ShBRE", "extra_answers": ["In the closet", "Hanging up in the closet in the first bedroom", "in the bedroom closet", "hanging on the rack in the bedroom closet"], "position": [-2.8504385948181152, 2.7961301803588867, -0.9476851224899292], "rotation": [-0.9700993895530701, 0.0, 0.24270813167095184, 0.0], "object_id": null, "class": null}, {"question": "What is hanging from the ceiling in the bedroom?", "answer": "A dome light", "category": "object recognition", "question_id": "08e8e5fd-31a3-466b-afd6-fa171f1d9de4", "episode_history": "00813-svBbv1Pavdk", "position": [4.655920505523682, 0.07754674553871155, 6.011612892150879], "rotation": [-0.015132062137126923, 0.0, 0.9998854994773865, 0.0], "object_id": null, "class": null}, {"question": "What is the gold object on the nightstand?", "answer": "A nightlamp", "category": "object recognition", "question_id": "6b8f1b52-25fa-47bc-a3a6-a2a43e834605", "episode_history": "00813-svBbv1Pavdk", "position": [4.655920505523682, 0.07754674553871155, 6.011612892150879], "rotation": [-0.015132062137126923, 0.0, 0.9998854994773865, 0.0], "object_id": null, "class": null}, {"question": "Is the coat closet in the living room open?", "answer": "No.", "category": "object state recognition", "question_id": "9acfbdd3-bc51-4010-ae1e-a28a949731d5", "episode_history": "00813-svBbv1Pavdk", "position": [4.655920505523682, 0.07754674553871155, 6.011612892150879], "rotation": [-0.015132062137126923, 0.0, 0.9998854994773865, 0.0], "object_id": null, "class": null}, {"question": "Where can I sit and eat if I don't want to use the dining table?", "answer": "Use the kitchen bar counter", "category": "functional reasoning", "question_id": "bf4960d4-469f-49bc-8594-9b994049fa77", "episode_history": "00813-svBbv1Pavdk", "position": [4.655920505523682, 0.07754674553871155, 6.011612892150879], "rotation": [-0.015132062137126923, 0.0, 0.9998854994773865, 0.0], "object_id": null, "class": null}, {"question": "What can I use to cool down the living room?", "answer": "The thermostat", "category": "functional reasoning", "question_id": "6a13d4a2-4866-40e7-8f10-d1ec12573dc2", "episode_history": "00813-svBbv1Pavdk", "position": [4.655920505523682, 0.07754674553871155, 6.011612892150879], "rotation": [-0.015132062137126923, 0.0, 0.9998854994773865, 0.0], "object_id": null, "class": null}, {"question": "Is this home on the first floor?", "answer": "No", "category": "world knowledge", "question_id": "41693f7e-4192-495e-9b4e-b238432c6424", "episode_history": "00813-svBbv1Pavdk", "position": [4.655920505523682, 0.07754674553871155, 6.011612892150879], "rotation": [-0.015132062137126923, 0.0, 0.9998854994773865, 0.0], "object_id": null, "class": null}, {"question": "What is the yellow object hanging on the wall?", "answer": "A painting depicting a man", "category": "object recognition", "question_id": "4decde3d-5ab8-43db-893c-c3f3f80bcc76", "episode_history": "00807-rsggHU7g7dh", "position": [-4.03098201751709, 0.2956973910331726, -1.1597659587860107], "rotation": [0.9977468848228455, 0.0, 0.06709049642086029, 0.0], "object_id": null, "class": null}, {"question": "What color are the kitchen cabinets?", "answer": "Grey", "category": "attribute recognition", "question_id": "6bff2ba3-5b68-4d77-a302-1640cc06dd15", "episode_history": "00807-rsggHU7g7dh", "position": [-4.03098201751709, 0.2956973910331726, -1.1597659587860107], "rotation": [0.9977468848228455, 0.0, 0.06709049642086029, 0.0], "object_id": null, "class": null}, {"question": "What color is the trim around the mirror?", "answer": "Gold", "category": "attribute recognition", "question_id": "36ad6cce-7cd1-429e-b75a-581dc6849603", "episode_history": "00807-rsggHU7g7dh", "position": [-4.03098201751709, 0.2956973910331726, -1.1597659587860107], "rotation": [0.9977468848228455, 0.0, 0.06709049642086029, 0.0], "object_id": null, "class": null}, {"question": "Are the kitchen bar chairs tucked in?", "answer": "Yes.", "category": "object state recognition", "question_id": "2449be8f-1320-4061-beb0-2797f5766c73", "episode_history": "00807-rsggHU7g7dh", "position": [-4.03098201751709, 0.2956973910331726, -1.1597659587860107], "rotation": [0.9977468848228455, 0.0, 0.06709049642086029, 0.0], "object_id": null, "class": null}, {"question": "What can I use to check my appearance?", "answer": "The mirror above the wooden table near the kitchen.", "category": "functional reasoning", "question_id": "8471794d-32cd-4989-8cec-91118eb43b67", "episode_history": "00807-rsggHU7g7dh", "position": [-4.03098201751709, 0.2956973910331726, -1.1597659587860107], "rotation": [0.9977468848228455, 0.0, 0.06709049642086029, 0.0], "object_id": null, "class": null}, {"question": "What is the picture seen from the kitchen depict?", "answer": "A man and a child, looking at something, standing in between trees", "category": "world knowledge", "question_id": "d5b18be3-2d0c-4653-9706-7c33159de7a9", "episode_history": "00807-rsggHU7g7dh", "position": [-4.03098201751709, 0.2956973910331726, -1.1597659587860107], "rotation": [0.9977468848228455, 0.0, 0.06709049642086029, 0.0], "object_id": null, "class": null}, {"question": "What is the yellow object on top of the ouch?", "answer": "A blanket", "category": "object recognition", "question_id": "447e4e2d-7010-4672-b8e0-eb5246430499", "episode_history": "00830-5jp3fCRSRjc", "position": [0.7293251752853394, 0.17085890471935272, 4.1670823097229], "rotation": [-0.9987487196922302, 0.0, -0.05001003295183182, 0.0], "object_id": null, "class": null}, {"question": "What color is the staircase railing?", "answer": "Dark brown", "category": "attribute recognition", "question_id": "872e8692-1e2a-4f7e-8ceb-7a85378be97d", "episode_history": "00830-5jp3fCRSRjc", "position": [0.7293251752853394, 0.17085890471935272, 4.1670823097229], "rotation": [-0.9987487196922302, 0.0, -0.05001003295183182, 0.0], "object_id": null, "class": null}, {"question": "Is the bedroom door open?", "answer": "Yes", "category": "object state recognition", "question_id": "4d127d5e-1a90-468c-93a0-0473c2d1623f", "episode_history": "00830-5jp3fCRSRjc", "position": [0.7293251752853394, 0.17085890471935272, 4.1670823097229], "rotation": [-0.9987487196922302, 0.0, -0.05001003295183182, 0.0], "object_id": null, "class": null}, {"question": "Where is the china cabinet?", "answer": "To the left of the dining table", "category": "object localization", "question_id": "8345f4b2-0850-495b-a957-16cb9cd66f4e", "episode_history": "00830-5jp3fCRSRjc", "extra_answers": ["in the dining room", "next to the dining table", "in the dining table", "on the left side of the room"], "position": [0.7293251752853394, 0.17085890471935272, 4.1670823097229], "rotation": [-0.9987487196922302, 0.0, -0.05001003295183182, 0.0], "object_id": null, "class": null}, {"question": "What color are the air vents below the window?", "answer": "Gold", "category": "attribute recognition", "question_id": "26bd014e-529f-4deb-bcfd-261f35ac7ff2", "episode_history": "00822-nrA1tAA17Yp", "position": [5.2473649978637695, 0.4810161590576172, -4.982304573059082], "rotation": [0.9595709443092346, 0.0, 0.2814669609069824, 0.0], "object_id": null, "class": null}, {"question": "What is above the piano?", "answer": "A painting", "category": "spatial understanding", "question_id": "e2a55cb0-a883-4dd6-9b2f-239d92ebd8bc", "episode_history": "00822-nrA1tAA17Yp", "position": [5.2473649978637695, 0.4810161590576172, -4.982304573059082], "rotation": [0.9595709443092346, 0.0, 0.2814669609069824, 0.0], "object_id": null, "class": null}, {"question": "Can this home be used for a large dinner party?", "answer": "Yes.", "category": "world knowledge", "question_id": "8b7d2afd-2a77-4f2b-afe1-b1751d890db4", "episode_history": "00822-nrA1tAA17Yp", "position": [5.2473649978637695, 0.4810161590576172, -4.982304573059082], "rotation": [0.9595709443092346, 0.0, 0.2814669609069824, 0.0], "object_id": null, "class": null}, {"question": "Is the person who lives here an avid reader?", "answer": "Yes.", "category": "world knowledge", "question_id": "2f73aba9-c2c7-4f97-b3e4-2435960763b9", "episode_history": "00822-nrA1tAA17Yp", "position": [5.2473649978637695, 0.4810161590576172, -4.982304573059082], "rotation": [0.9595709443092346, 0.0, 0.2814669609069824, 0.0], "object_id": null, "class": null}, {"question": "What is above the wooden table in the living room?", "answer": "A blue, white, and orange painting", "category": "object recognition", "question_id": "a605c40f-96e7-4bec-a1cb-6d48e88e39cd", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "What color are the pillows in the kitchen?", "answer": "Blue", "category": "attribute recognition", "question_id": "3a5be057-47d2-4f78-98a9-729ef19b3d8b", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "Is the light above the sink turned on?", "answer": "Yes", "category": "object state recognition", "question_id": "4dbd213e-56cd-481a-8ff5-ed9a8d636dbc", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "Where can I get recipes for what to cook?", "answer": "The open cookbook on the kitchen counter top", "category": "functional reasoning", "question_id": "ae19adeb-498a-4814-b955-e0af05623f9b", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "Where can I get drinking water in the kitchen?", "answer": "From water dispenser in the fridge", "category": "world knowledge", "question_id": "4cc4212e-0db2-421f-8bb5-93817e58f9b4", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "Is the sliding door open or closed?", "answer": "Closed", "category": "object state recognition", "question_id": "633ca326-2027-4316-8c20-ef4debde39d2", "episode_history": "00833-dHwjuKfkRUR", "position": [8.02768325805664, 1.974287509918213, 2.812986135482788], "rotation": [-0.6783210039138794, 0.0, 0.7347657680511475, 0.0], "object_id": null, "class": null}, {"question": "Are the walls painted?", "answer": "No", "category": "world knowledge", "question_id": "7131770c-d338-4dfa-b778-0dd5a00a4ef1", "episode_history": "00833-dHwjuKfkRUR", "position": [8.02768325805664, 1.974287509918213, 2.812986135482788], "rotation": [-0.6783210039138794, 0.0, 0.7347657680511475, 0.0], "object_id": null, "class": null}, {"question": "Is the bedroom furnished and ready to live in?", "answer": "No.", "category": "world knowledge", "question_id": "e22162c7-9c4d-46f0-8dd5-560a9c4f0dad", "episode_history": "00833-dHwjuKfkRUR", "position": [8.02768325805664, 1.974287509918213, 2.812986135482788], "rotation": [-0.6783210039138794, 0.0, 0.7347657680511475, 0.0], "object_id": null, "class": null}, {"question": "What color is the bed frame?", "answer": "White", "category": "attribute recognition", "question_id": "e87b90d9-77d5-4f99-b44a-ad1d11480334", "episode_history": "00808-y9hT<PERSON>ugGdiq", "position": [-2.7008867263793945, 0.06139111518859863, 1.6667211055755615], "rotation": [0.9634894132614136, 0.0, 0.2677465081214905, 0.0], "object_id": null, "class": null}, {"question": "Where can I get a drink of water?", "answer": "From the water dispenser in the fridge", "category": "world knowledge", "question_id": "13d097e7-12c7-48e0-92c4-9667fc7f9c60", "episode_history": "00808-y9hT<PERSON>ugGdiq", "position": [-2.7008867263793945, 0.06139111518859863, 1.6667211055755615], "rotation": [0.9634894132614136, 0.0, 0.2677465081214905, 0.0], "object_id": null, "class": null}, {"question": "Where are the paper towels?", "answer": "Above the dishwasher", "category": "object localization", "question_id": "b38045c3-cf3d-43e3-8fee-a085b89a4d3a", "episode_history": "00808-y9hT<PERSON>ugGdiq", "extra_answers": ["On the kitchen, at the right of the sink", "On the kitchen above the dishwasher", "near the kitchen breakfast bar", "next to the toaster"], "position": [-2.7008867263793945, 0.06139111518859863, 1.6667211055755615], "rotation": [0.9634894132614136, 0.0, 0.2677465081214905, 0.0], "object_id": null, "class": null}, {"question": "Where is the umbrella?", "answer": "On the balcony outside", "category": "object localization", "question_id": "5a8b3936-43e0-4474-ac15-efaf488265a1", "episode_history": "00808-y9hT<PERSON>ugGdiq", "extra_answers": ["Outside the house, on the balcony", "On the porche", "on the back deck", "outside over the table"], "position": [-2.7008867263793945, 0.06139111518859863, 1.6667211055755615], "rotation": [0.9634894132614136, 0.0, 0.2677465081214905, 0.0], "object_id": null, "class": null}, {"question": "What material are the blue pillow's pillowcases made out of?", "answer": "Velvet", "category": "attribute recognition", "question_id": "cb38e809-d5b7-471d-b412-5bc13bd20413", "episode_history": "00862-LT9Jq6dN3Ea", "position": [1.8005813360214233, 3.306027889251709, -3.4901986122131348], "rotation": [0.9252249002456665, 0.0, 0.3794192373752594, 0.0], "object_id": null, "class": null}, {"question": "Where can I wash my hands on the second story of the house?", "answer": "The bathroom sink", "category": "functional reasoning", "question_id": "50d2cdeb-86e2-46d7-9c32-ef91e66176f0", "episode_history": "00862-LT9Jq6dN3Ea", "position": [1.8005813360214233, 3.306027889251709, -3.4901986122131348], "rotation": [0.9252249002456665, 0.0, 0.3794192373752594, 0.0], "object_id": null, "class": null}, {"question": "I need to blow my nose while taking a bath, what can I use?", "answer": "There is toilet paper next to the tub.", "category": "world knowledge", "question_id": "0c81b6f8-2d46-4e07-a9d3-a019729d5570", "episode_history": "00862-LT9Jq6dN3Ea", "position": [1.8005813360214233, 3.306027889251709, -3.4901986122131348], "rotation": [0.9252249002456665, 0.0, 0.3794192373752594, 0.0], "object_id": null, "class": null}, {"question": "What room is the orchid in?", "answer": "The bathroom.", "category": "object localization", "question_id": "bc1af4f3-5241-4606-8315-ca46d88d7d84", "episode_history": "00862-LT9Jq6dN3Ea", "extra_answers": ["bathroom", "the bathroom", "the room with the bath tub", "The room with the shower"], "position": [1.8005813360214233, 3.306027889251709, -3.4901986122131348], "rotation": [0.9252249002456665, 0.0, 0.3794192373752594, 0.0], "object_id": null, "class": null}, {"question": "What kind of pants is the man in the mirror wearing?", "answer": "<PERSON><PERSON>", "category": "attribute recognition", "question_id": "06c9b25c-b117-4b8f-a052-6fd71b2bb043", "episode_history": "00871-VBzV5z6i1WS", "position": [-6.768316745758057, 0.046541884541511536, -2.06489896774292], "rotation": [0.9770541191101074, 0.0, 0.21299141645431519, 0.0], "object_id": null, "class": null}, {"question": "It's too bright in the living room, how can I make it darker?", "answer": "Lower the shades over the porch door.", "category": "functional reasoning", "question_id": "7ad70423-200c-42a8-8e6a-c471f171782e", "episode_history": "00871-VBzV5z6i1WS", "position": [-6.768316745758057, 0.046541884541511536, -2.06489896774292], "rotation": [0.9770541191101074, 0.0, 0.21299141645431519, 0.0], "object_id": null, "class": null}, {"question": "If I sit in the armchair in the living room, what animal can I touch with my right hand?", "answer": "An elephant", "category": "world knowledge", "question_id": "5fc88f40-890b-4a30-8b97-d404e8f5c330", "episode_history": "00871-VBzV5z6i1WS", "position": [-6.768316745758057, 0.046541884541511536, -2.06489896774292], "rotation": [0.9770541191101074, 0.0, 0.21299141645431519, 0.0], "object_id": null, "class": null}, {"question": "Where is the microwave?", "answer": "On top of the refrigerator", "category": "object localization", "question_id": "65ec009e-d173-4e49-9168-f48fd20308f1", "episode_history": "00845-c3WKCnkEdha", "extra_answers": ["above the fridge", "in the kitchen above the fridge", "Opposite to the table with \"five kitchen\" written across it", "Above the fridge in the hallway"], "position": [8.092799186706543, 0.10569107532501221, 1.0895073413848877], "rotation": [0.6123970746994019, 0.0, 0.7905503511428833, 0.0], "object_id": null, "class": null}, {"question": "Where is the pillow with a hexagonal pattern?", "answer": "On the bed in the second bedroom.", "category": "object localization", "question_id": "46a04f3a-56a5-4547-9cd9-c683919c0eb1", "episode_history": "00845-c3WKCnkEdha", "extra_answers": ["on the twin bed", "on the wooden twin bed", "On the bed in the room with the pink curtains", "On top of the bed in the second bedroom"], "position": [8.092799186706543, 0.10569107532501221, 1.0895073413848877], "rotation": [0.6123970746994019, 0.0, 0.7905503511428833, 0.0], "object_id": null, "class": null}, {"question": "What type of numbers are on the clock?", "answer": "Roman numerals", "category": "attribute recognition", "question_id": "0df60236-15ad-4166-a31a-a98d14214fdb", "episode_history": "00848-ziup5kvtCCR", "position": [0.26384106278419495, 0.021223172545433044, 7.149479389190674], "rotation": [0.9547726511955261, 0.0, 0.29733675718307495, 0.0], "object_id": null, "class": null}, {"question": "Where is the toy car?", "answer": "To the left of the television in the first bedroom.", "category": "object localization", "question_id": "d0165de2-29aa-44d1-8689-ff13cd573c79", "episode_history": "00806-tQ5s4ShP627", "extra_answers": ["next to the bedroom tv", "on the cabinet in the bedroom", "to the left of the television", "in the first bedroom across the bed"], "position": [-2.169149160385132, 0.16448144614696503, 3.818364143371582], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "Where do I reach for laundry detergent?", "answer": "For the shelf above the washing machine.", "category": "spatial understanding", "question_id": "ba5f1c9b-9a41-4a84-829b-f9b8ccd19b69", "episode_history": "00876-mv2HUxq3B53", "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "Is the toilet seat open or closed?", "answer": "Closed.", "category": "object state recognition", "question_id": "f17869a2-2a4d-4ce4-b262-cb69618e3394", "episode_history": "00876-mv2HUxq3B53", "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "Is the television turned on or off?", "answer": "On", "category": "object state recognition", "question_id": "e0d20472-8fa6-4e8d-880d-22d4eed3fbb8", "episode_history": "00876-mv2HUxq3B53", "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "I want to check my outfit for a dinner party, how can I do this?", "answer": "Using the large mirror outside the first bedroom.", "category": "functional reasoning", "question_id": "fc9d2a18-6197-4c8b-abd8-be0c493e5450", "episode_history": "00876-mv2HUxq3B53", "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "what color are the numbers written on the wall clock?", "answer": "white", "category": "attribute recognition", "question_id": "c1b2ccf5-b56d-4ced-9cec-eaf62fedc675", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "what is between the fruit bowl and knife set?", "answer": "a container of spices", "category": "spatial understanding", "question_id": "a5c5bb29-700a-4ef5-b17d-aaa47bb0ef3f", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "is the table in the living room completely clean?", "answer": "no", "category": "spatial understanding", "question_id": "b05e7b30-6a4d-4381-9d05-a42ed0c90e30", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "where can I keep a new knife I got?", "answer": "there is a knife holder in the kitchen counter next to the gas stove.", "category": "world knowledge", "question_id": "1dcdd225-eba2-4ba1-97b6-c4cdc7ca4e9b", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "what color is the flower in the bottom floor?", "answer": "pink", "category": "attribute recognition", "question_id": "1eb05aa7-89a0-4e9f-a06d-e05a2e4e8e63", "episode_history": "00890-6s7QHgap2fW", "position": [4.158755779266357, 2.8994359970092773, -1.0809082984924316], "rotation": [-0.14734554290771484, 0.0, 0.9890851378440857, 0.0], "object_id": null, "class": null}, {"question": "what color is the chair?", "answer": "black", "category": "attribute recognition", "question_id": "11574d0e-54bb-4900-b230-0f76f1f43266", "episode_history": "00890-6s7QHgap2fW", "position": [4.158755779266357, 2.8994359970092773, -1.0809082984924316], "rotation": [-0.14734554290771484, 0.0, 0.9890851378440857, 0.0], "object_id": null, "class": null}, {"question": "what is between the closet and the bedroom?", "answer": "a mirror", "category": "object recognition", "question_id": "df5a6203-24a0-40d7-b178-31fb02db71ef", "episode_history": "00868-vd3HHTEpmyA", "position": [-2.5557174682617188, 3.2388510704040527, 6.238373279571533], "rotation": [-0.99361652135849, 0.0, 0.1128106564283371, 0.0], "object_id": null, "class": null}, {"question": "what color is the door of the bedroom?", "answer": "white", "category": "attribute recognition", "question_id": "182db45a-eeda-4ccd-841b-20ce864f5c1e", "episode_history": "00868-vd3HHTEpmyA", "position": [-2.5557174682617188, 3.2388510704040527, 6.238373279571533], "rotation": [-0.99361652135849, 0.0, 0.1128106564283371, 0.0], "object_id": null, "class": null}, {"question": "are the electric lamps near the staircase on or off?", "answer": "on", "category": "object state recognition", "question_id": "4446bd7d-25fa-4996-9b23-9337b8512f07", "episode_history": "00868-vd3HHTEpmyA", "position": [-2.5557174682617188, 3.2388510704040527, 6.238373279571533], "rotation": [-0.99361652135849, 0.0, 0.1128106564283371, 0.0], "object_id": null, "class": null}, {"question": "what is special about the wall in the living room?", "answer": "it seems to be made of stone", "category": "world knowledge", "question_id": "11da38f3-c32f-4443-bd33-6a2c1ec22a64", "episode_history": "00827-BAbdmeyTvMZ", "position": [8.8579740524292, 0.19520819187164307, 2.8207812309265137], "rotation": [-0.4048997163772583, 0.0, 0.9143611192703247, 0.0], "object_id": null, "class": null}, {"question": "where is the standing lamp?", "answer": "next to the bed in the bedroom", "category": "object localization", "question_id": "cdb2760c-33d0-4e19-8ddc-494f9874dfb3", "episode_history": "00827-BAbdmeyTvMZ", "extra_answers": ["in the bedroom", "to the left of the bed", "the bedroom", "The room with the bed and the bathroom"], "position": [8.8579740524292, 0.19520819187164307, 2.8207812309265137], "rotation": [-0.4048997163772583, 0.0, 0.9143611192703247, 0.0], "object_id": null, "class": null}, {"question": "what is behind the monitor screen?", "answer": "window", "category": "object recognition", "question_id": "98f5190a-b4b0-4bcb-83d0-43dfc39dba85", "episode_history": "00818-rJhMRvNn4DS", "position": [-7.698716163635254, 3.0333285331726074, -1.8840349912643433], "rotation": [-0.6828608512878418, 0.0, 0.7305485010147095, 0.0], "object_id": null, "class": null}, {"question": "what color is the tripod?", "answer": "black", "category": "attribute recognition", "question_id": "860923a7-097b-4df5-8a62-59975c3d2a83", "episode_history": "00818-rJhMRvNn4DS", "position": [-7.698716163635254, 3.0333285331726074, -1.8840349912643433], "rotation": [-0.6828608512878418, 0.0, 0.7305485010147095, 0.0], "object_id": null, "class": null}, {"question": "are the blinds in the workroom shut or not?", "answer": "not shut", "category": "object state recognition", "question_id": "7473836e-84ba-4d9c-a86e-4da760d670f4", "episode_history": "00818-rJhMRvNn4DS", "position": [-7.698716163635254, 3.0333285331726074, -1.8840349912643433], "rotation": [-0.6828608512878418, 0.0, 0.7305485010147095, 0.0], "object_id": null, "class": null}, {"question": "what is infront of the blackboard on the wall?", "answer": "a small kids table and chair set", "category": "object recognition", "question_id": "226ab7fe-8b53-4842-b313-1e7644771cb2", "episode_history": "00856-FnSn2KSrALj", "position": [2.165637493133545, 0.10760803520679474, 10.395718574523926], "rotation": [0.9668591022491455, 0.0, 0.25531068444252014, 0.0], "object_id": null, "class": null}, {"question": "what is between the lamp and the side table in the bedroom?", "answer": "electric switch", "category": "object recognition", "question_id": "c7c8c496-b3d2-4370-b321-d4274ffda701", "episode_history": "00856-FnSn2KSrALj", "position": [2.165637493133545, 0.10760803520679474, 10.395718574523926], "rotation": [0.9668591022491455, 0.0, 0.25531068444252014, 0.0], "object_id": null, "class": null}, {"question": "what shape are the decorations above the bed?", "answer": "triangle", "category": "attribute recognition", "question_id": "44a23f96-b643-4e1b-94ad-48687d0f38b7", "episode_history": "00856-FnSn2KSrALj", "position": [2.165637493133545, 0.10760803520679474, 10.395718574523926], "rotation": [0.9668591022491455, 0.0, 0.25531068444252014, 0.0], "object_id": null, "class": null}, {"question": "are the lamps in the second room on or off?", "answer": "on", "category": "object state recognition", "question_id": "04c770eb-c1a7-44c4-b91f-8aa24e2dbed9", "episode_history": "00856-FnSn2KSrALj", "position": [2.165637493133545, 0.10760803520679474, 10.395718574523926], "rotation": [0.9668591022491455, 0.0, 0.25531068444252014, 0.0], "object_id": null, "class": null}, {"question": "where is the star drawn?", "answer": "on the blackboard", "category": "object localization", "question_id": "0ef0ebd1-db05-4f87-adc7-d01a640c1eed", "episode_history": "00856-FnSn2KSrALj", "extra_answers": ["on the chalkboard", "on the chalkboard next to the small table", "on the blackboard in the kids room", "on the blackboard across from the bunk beds"], "position": [2.165637493133545, 0.10760803520679474, 10.395718574523926], "rotation": [0.9668591022491455, 0.0, 0.25531068444252014, 0.0], "object_id": null, "class": null}, {"question": "where precisely are the markers in the study room?", "answer": "on the whiteboard bottom-right corner", "category": "spatial understanding", "question_id": "de97a986-30c3-4e0b-92dc-77ba1900cf8d", "episode_history": "00863-b28CWbpQvor", "position": [-9.421932220458984, 0.15163874626159668, 18.819976806640625], "rotation": [0.9770541191101074, 0.0, 0.21299141645431519, 0.0], "object_id": null, "class": null}, {"question": "how can I clean the whiteboard?", "answer": "using the eraser attached to the whiteboard", "category": "functional reasoning", "question_id": "acebe630-9d99-4897-bd0f-028038e5baaa", "episode_history": "00863-b28CWbpQvor", "position": [-9.421932220458984, 0.15163874626159668, 18.819976806640625], "rotation": [0.9770541191101074, 0.0, 0.21299141645431519, 0.0], "object_id": null, "class": null}, {"question": "what room is the surface cleaner in?", "answer": "the room with the whiteboard", "category": "object localization", "question_id": "8a914303-067c-44ba-b8a3-2fd72d3f4396", "episode_history": "00863-b28CWbpQvor", "extra_answers": ["The office room", "the room with yellow walls", "the office", "the room with the desk and computer"], "position": [-9.421932220458984, 0.15163874626159668, 18.819976806640625], "rotation": [0.9770541191101074, 0.0, 0.21299141645431519, 0.0], "object_id": null, "class": null}, {"question": "where is the fan?", "answer": "in the dining room besides the kitchen", "category": "object localization", "question_id": "babe466a-4ee8-4afd-a8f9-964793a5d425", "episode_history": "00874-uSKXQ5fFg6u", "extra_answers": ["over the dining table", "in the dining room", "above the dining table", "on the ceiling in the room next to the kitchen"], "position": [6.305362701416016, 0.20773282647132874, 0.9874666929244995], "rotation": [-0.12166600674390793, 0.0, 0.9925711154937744, 0.0], "object_id": null, "class": null}, {"question": "where is the lamp that is switched on?", "answer": "in the bedroom on the above floor`", "category": "object localization", "question_id": "41db3bb6-0bb5-4fcb-95b1-f19a32be4184", "episode_history": "00801-HaxA7YrQdEC", "extra_answers": ["on the black chest of drawers in the bedroom", "on the black chest of drawers", "On the black cabinet in the bedroom", "To the left of the brown painting in the bedroom"], "position": [-7.15059232711792, 3.051248073577881, 4.236446380615234], "rotation": [-0.9885613918304443, 0.0, 0.15081894397735596, 0.0], "object_id": null, "class": null}, {"question": "What is on top of the washing machine?", "answer": "The dryer", "category": "spatial understanding", "question_id": "b70465b8-53a3-436f-b12f-2d8cdf8f1856", "episode_history": "00861-GLAQ4DNUx5U", "position": [0.7373262047767639, 1.207113265991211, 0.9808619022369385], "rotation": [0.9252249002456665, 0.0, 0.3794192373752594, 0.0], "object_id": null, "class": null}, {"question": "How can I dry my towel?", "answer": "By hanging it on the towel warmer.", "category": "functional reasoning", "question_id": "206ca121-9185-484d-ab22-acfb082b1359", "episode_history": "00861-GLAQ4DNUx5U", "position": [0.7373262047767639, 1.207113265991211, 0.9808619022369385], "rotation": [0.9252249002456665, 0.0, 0.3794192373752594, 0.0], "object_id": null, "class": null}, {"question": "How can I reach the wine glasses if I'm too short?", "answer": "Using the step-stool in the laundry room.", "category": "functional reasoning", "question_id": "c64f520c-6450-413c-99da-979be386ff86", "episode_history": "00861-GLAQ4DNUx5U", "position": [0.7373262047767639, 1.207113265991211, 0.9808619022369385], "rotation": [0.9252249002456665, 0.0, 0.3794192373752594, 0.0], "object_id": null, "class": null}, {"question": "What room is the potted cactus in?", "answer": "The bathroom", "category": "object localization", "question_id": "33639e66-332d-4824-82ef-e1bf13e94ccb", "episode_history": "00861-GLAQ4DNUx5U", "extra_answers": ["restroom", "bathroom", "In the bathroom", "On the counter to the right of the sink in the bathroom"], "position": [0.7373262047767639, 1.207113265991211, 0.9808619022369385], "rotation": [0.9252249002456665, 0.0, 0.3794192373752594, 0.0], "object_id": null, "class": null}, {"question": "What is to the left of the black office chair?", "answer": "A recumbent exercise bicycle", "category": "object recognition", "question_id": "56c62311-8d4b-470d-a716-49fef718fcff", "episode_history": "00842-hkr2MGpHD6B", "position": [1.6290583610534668, -2.3820176124572754, 3.999110698699951], "rotation": [0.9849280118942261, 0.0, 0.1729649007320404, 0.0], "object_id": null, "class": null}, {"question": "What is next to the posters on the wall in the staircase?", "answer": " A calendar.", "category": "object recognition", "question_id": "b0740f05-dbf1-4835-b16e-62d01d371a78", "episode_history": "00842-hkr2MGpHD6B", "position": [1.6290583610534668, -2.3820176124572754, 3.999110698699951], "rotation": [0.9849280118942261, 0.0, 0.1729649007320404, 0.0], "object_id": null, "class": null}, {"question": "Are all of the bulbs in the ceiling fan over the dining room table lit?", "answer": "No", "category": "object state recognition", "question_id": "872e9d7f-752d-47bb-aa7d-50a23be3ea69", "episode_history": "00842-hkr2MGpHD6B", "position": [1.6290583610534668, -2.3820176124572754, 3.999110698699951], "rotation": [0.9849280118942261, 0.0, 0.1729649007320404, 0.0], "object_id": null, "class": null}, {"question": "I want to do a quick workout, where can I do this?", "answer": "Using the exercise bike in the basement.", "category": "functional reasoning", "question_id": "d6d33031-738b-462d-ac53-2c2df150083e", "episode_history": "00842-hkr2MGpHD6B", "position": [1.6290583610534668, -2.3820176124572754, 3.999110698699951], "rotation": [0.9849280118942261, 0.0, 0.1729649007320404, 0.0], "object_id": null, "class": null}, {"question": "Where is the large green plant?", "answer": "In the entryway.", "category": "object localization", "question_id": "197342c3-490c-4d6c-9fc9-e4003bc61c17", "episode_history": "00842-hkr2MGpHD6B", "extra_answers": ["in the living room with the sofa", "in the living room", "On the hallway between the living room and the kitchen", "In the living room"], "position": [1.6290583610534668, -2.3820176124572754, 3.999110698699951], "rotation": [0.9849280118942261, 0.0, 0.1729649007320404, 0.0], "object_id": null, "class": null}, {"question": "what color is the picture frame", "answer": "black", "category": "attribute recognition", "question_id": "6b7a2be4-beaa-4023-b031-81f8ecb5c94f", "episode_history": "00817-X4qjx5vquwH", "position": [2.995811700820923, 0.07250833511352539, -1.9891128540039062], "rotation": [-0.4310588836669922, 0.0, 0.902323842048645, 0.0], "object_id": null, "class": null}, {"question": "are there objects on top of the blue drawer ", "answer": "no", "category": "object state recognition", "question_id": "4f65eebc-f602-44ae-8c37-e903f5d940c4", "episode_history": "00817-X4qjx5vquwH", "position": [2.995811700820923, 0.07250833511352539, -1.9891128540039062], "rotation": [-0.4310588836669922, 0.0, 0.902323842048645, 0.0], "object_id": null, "class": null}, {"question": "where can I store clothes", "answer": "in the drawer on the second floor", "category": "functional reasoning", "question_id": "2e1f37e6-0259-4cdb-817e-ba1d015458f6", "episode_history": "00817-X4qjx5vquwH", "position": [2.995811700820923, 0.07250833511352539, -1.9891128540039062], "rotation": [-0.4310588836669922, 0.0, 0.902323842048645, 0.0], "object_id": null, "class": null}, {"question": "on which floor is the washing machine", "answer": "on the second floor", "category": "object localization", "question_id": "59df90ad-e54a-48a3-8ac6-7c00e48f0b3d", "episode_history": "00817-X4qjx5vquwH", "extra_answers": ["the second floor", "the upper floor", "The second floor.", "In the room at the top of the stairs on the second floor."], "position": [2.995811700820923, 0.07250833511352539, -1.9891128540039062], "rotation": [-0.4310588836669922, 0.0, 0.902323842048645, 0.0], "object_id": null, "class": null}, {"question": "what is the object to the right of the TV", "answer": "a clock", "category": "object recognition", "question_id": "225c132a-1ec6-47b3-8f5e-887b91168b93", "episode_history": "00838-kJJyRFXVpx2", "position": [0.7598791122436523, 3.5049240589141846, 5.310320854187012], "rotation": [-0.18368904292583466, 0.0, 0.9829844236373901, 0.0], "object_id": null, "class": null}, {"question": "what color are the bed covers", "answer": "blue", "category": "attribute recognition", "question_id": "ce8acdaa-800e-4c8a-a3a2-42297c2b9526", "episode_history": "00838-kJJyRFXVpx2", "position": [0.7598791122436523, 3.5049240589141846, 5.310320854187012], "rotation": [-0.18368904292583466, 0.0, 0.9829844236373901, 0.0], "object_id": null, "class": null}, {"question": "what is above the right bed", "answer": "a window", "category": "spatial understanding", "question_id": "62627a1e-e41b-480d-9608-48a154b260bc", "episode_history": "00838-kJJyRFXVpx2", "position": [0.7598791122436523, 3.5049240589141846, 5.310320854187012], "rotation": [-0.18368904292583466, 0.0, 0.9829844236373901, 0.0], "object_id": null, "class": null}, {"question": "I can't open the windows, how can I cool down the bedroom", "answer": "turn on ceiling fan", "category": "world knowledge", "question_id": "62ee487f-ee36-4126-b427-41d7447da702", "episode_history": "00838-kJJyRFXVpx2", "position": [0.7598791122436523, 3.5049240589141846, 5.310320854187012], "rotation": [-0.18368904292583466, 0.0, 0.9829844236373901, 0.0], "object_id": null, "class": null}, {"question": "what is the pink object on the bed", "answer": "a pillow", "category": "object recognition", "question_id": "991967d0-f7ba-4b8e-af60-16c7b9ca00a5", "episode_history": "00805-SUHsP6z2gcJ", "position": [-0.8588698506355286, 0.1239565908908844, -0.49567723274230957], "rotation": [0.6349913477897644, 0.0, 0.7725192904472351, 0.0], "object_id": null, "class": null}, {"question": "what is on top of the drawer in the bedroom", "answer": "a TV", "category": "object recognition", "question_id": "95762878-541e-4f5c-b071-abe79a0393f3", "episode_history": "00805-SUHsP6z2gcJ", "position": [-0.8588698506355286, 0.1239565908908844, -0.49567723274230957], "rotation": [0.6349913477897644, 0.0, 0.7725192904472351, 0.0], "object_id": null, "class": null}, {"question": "is there an area between the beds to place a glass of water", "answer": "yes", "category": "spatial understanding", "question_id": "af709fd8-dca2-4697-9548-07aa9d157d8e", "episode_history": "00805-SUHsP6z2gcJ", "position": [-0.8588698506355286, 0.1239565908908844, -0.49567723274230957], "rotation": [0.6349913477897644, 0.0, 0.7725192904472351, 0.0], "object_id": null, "class": null}, {"question": "is the door to the balcony open or closed", "answer": "open", "category": "object state recognition", "question_id": "297ec2f6-52fe-4dd2-a325-587510d53de7", "episode_history": "00805-SUHsP6z2gcJ", "position": [-0.8588698506355286, 0.1239565908908844, -0.49567723274230957], "rotation": [0.6349913477897644, 0.0, 0.7725192904472351, 0.0], "object_id": null, "class": null}, {"question": "can I clean the floor with a mop?", "answer": "yes", "category": "world knowledge", "question_id": "a4d9802f-5339-4b61-8c9a-42256441b86d", "episode_history": "00805-SUHsP6z2gcJ", "position": [-0.8588698506355286, 0.1239565908908844, -0.49567723274230957], "rotation": [0.6349913477897644, 0.0, 0.7725192904472351, 0.0], "object_id": null, "class": null}, {"question": "where are chairs to sit", "answer": "on the balcony", "category": "object localization", "question_id": "d843b020-4415-4efd-95e7-903f96d4eb26", "episode_history": "00805-SUHsP6z2gcJ", "extra_answers": ["outside the sliding doors", "on the porch / balcony", "On the balcony", "On the porche"], "position": [-0.8588698506355286, 0.1239565908908844, -0.49567723274230957], "rotation": [0.6349913477897644, 0.0, 0.7725192904472351, 0.0], "object_id": null, "class": null}, {"question": "can I use a mop to clean the stairs?", "answer": "no", "category": "world knowledge", "question_id": "122417bb-6bcd-4d2f-87dc-96be6ba6c262", "episode_history": "00891-cvZr5TUy5C5", "position": [0.6490447521209717, -2.84094500541687, 0.691494882106781], "rotation": [0.49729248881340027, 0.0, 0.8675829768180847, 0.0], "object_id": null, "class": null}, {"question": "do I need to install curtains to reduce the day light?", "answer": "no", "category": "world knowledge", "question_id": "5cccc0a8-288e-460a-ad2c-d36fcbaee644", "episode_history": "00891-cvZr5TUy5C5", "position": [0.6490447521209717, -2.84094500541687, 0.691494882106781], "rotation": [0.49729248881340027, 0.0, 0.8675829768180847, 0.0], "object_id": null, "class": null}, {"question": "Where is the picture of the flowers?", "answer": "Above the wooden nightstand ", "category": "object localization", "question_id": "da85d7b4-f3d3-44a4-ac2d-de022e39ff45", "episode_history": "00850-W7k2QWzBrFY", "extra_answers": ["Above the side table in the first bedroom.", "Over the table with the phone in the first bedroom.", "In the bedroom", "In the bedroom downstairs near the dining room"], "position": [-11.995431900024414, 0.05454225838184357, -5.0906476974487305], "rotation": [0.4380071461200714, 0.0, 0.8989714980125427, 0.0], "object_id": null, "class": null}, {"question": "Where is the light purple flower painting?", "answer": "Above the toilet in the bathroom upstairs", "category": "object localization", "question_id": "bc0caf6a-7684-4730-bc58-3717c1e57b38", "episode_history": "00811-7UrtFsADwob", "extra_answers": ["in the upstairs bathroom", "in the upstairs bathroom above the toilet", "in the bathroom", "in the bathroom above the toilet"], "position": [-0.9727550745010376, 2.863504409790039, -3.2301273345947266], "rotation": [0.3970239758491516, 0.0, 0.9178082942962646, 0.0], "object_id": null, "class": null}, {"question": "What can be seen through the window in the living room?", "answer": "Trees and hills", "category": "object recognition", "question_id": "911693d9-2d28-4ff2-83a9-c67b83753831", "episode_history": "00835-q3zU7Yy5E5s", "position": [-0.4913436472415924, 0.03521956503391266, -0.4102398455142975], "rotation": [0.661293089389801, 0.0, 0.7501276135444641, 0.0], "object_id": null, "class": null}, {"question": "Where is the fireplace?", "answer": "In the living room, to the right of the radiator and window", "category": "object localization", "question_id": "9b2d06e5-ca78-4519-a9ca-75c06209b770", "episode_history": "00835-q3zU7Yy5E5s", "extra_answers": ["In the corner of the living room.", "Next to the blue couch.", "in the living room ", "in the living room right next to the window"], "position": [-0.4913436472415924, 0.03521956503391266, -0.4102398455142975], "rotation": [0.661293089389801, 0.0, 0.7501276135444641, 0.0], "object_id": null, "class": null}, {"question": "What is in the right corner of the room downstairs?", "answer": "A bookshelf", "category": "object recognition", "question_id": "af4b62be-5f12-4180-8a3a-665152a7dfd9", "episode_history": "00823-7MXmsvcQjpJ", "position": [2.1335012912750244, 1.4296398162841797, -3.244293212890625], "rotation": [0.29250770807266235, 0.0, 0.9562631845474243, 0.0], "object_id": null, "class": null}, {"question": "Is the china cabinet full?", "answer": "Yes.", "category": "object state recognition", "question_id": "8dded29b-3c01-43bf-846b-b09b9b4ea439", "episode_history": "00823-7MXmsvcQjpJ", "position": [2.1335012912750244, 1.4296398162841797, -3.244293212890625], "rotation": [0.29250770807266235, 0.0, 0.9562631845474243, 0.0], "object_id": null, "class": null}, {"question": "Does this house have a backyard?", "answer": "Yes", "category": "world knowledge", "question_id": "8c57fa88-0550-4808-b081-095c709d68a8", "episode_history": "00823-7MXmsvcQjpJ", "position": [2.1335012912750244, 1.4296398162841797, -3.244293212890625], "rotation": [0.29250770807266235, 0.0, 0.9562631845474243, 0.0], "object_id": null, "class": null}, {"question": "What color is the chest surrounded by sofas?", "answer": "Red", "category": "attribute recognition", "question_id": "6f9d6ab6-d566-46d8-bd98-ad1c6460c2a8", "episode_history": "00814-p53SfW6mjZe", "position": [-16.332725524902344, 3.059805154800415, -2.9089789390563965], "rotation": [-0.4617184102535248, 0.0, 0.8870266079902649, 0.0], "object_id": null, "class": null}, {"question": "Where is the white sofa?", "answer": "In the living room", "category": "object localization", "question_id": "eb6335ed-c49e-408e-abcd-cce9636ec2b8", "episode_history": "00814-p53SfW6mjZe", "extra_answers": ["to the left of the gray couch", "In the room with the fireplace", "Downstairs in the living room", "In the living room downstairs near the chimney"], "position": [-16.332725524902344, 3.059805154800415, -2.9089789390563965], "rotation": [-0.4617184102535248, 0.0, 0.8870266079902649, 0.0], "object_id": null, "class": null}, {"question": "Can another cookie jar fit on the cookie jar shelf?", "answer": "yes", "category": "spatial understanding", "question_id": "d5f844fc-81cd-465e-aa90-e8ff8658c861", "episode_history": "00889-HMkoS756sz6", "position": [2.309299945831299, 2.7712581157684326, -4.815240859985352], "rotation": [0.9962098598480225, 0.0, -0.08698248863220215, 0.0], "object_id": null, "class": null}, {"question": "Is the backdoor open or closed?", "answer": "open", "category": "object state recognition", "question_id": "3412275e-e797-462e-820d-030317d9e323", "episode_history": "00889-HMkoS756sz6", "position": [2.309299945831299, 2.7712581157684326, -4.815240859985352], "rotation": [0.9962098598480225, 0.0, -0.08698248863220215, 0.0], "object_id": null, "class": null}, {"question": "Where can I access the attic?", "answer": "door at the top of the stairs", "category": "world knowledge", "question_id": "d9be5488-237b-41e0-bfac-3ba299d64203", "episode_history": "00889-HMkoS756sz6", "position": [2.309299945831299, 2.7712581157684326, -4.815240859985352], "rotation": [0.9962098598480225, 0.0, -0.08698248863220215, 0.0], "object_id": null, "class": null}, {"question": "Where is the yellow pillow? ", "answer": "The kitchen window", "category": "object localization", "question_id": "cf7a6ff1-4a97-4b6c-b78e-a70f40cdd80f", "episode_history": "00889-HMkoS756sz6", "extra_answers": ["on the bench in the kitchen", "in the nook in the kitchen", "On the daybed in the kitchen.", "On the couch on the windowsill in the kitchen."], "position": [2.309299945831299, 2.7712581157684326, -4.815240859985352], "rotation": [0.9962098598480225, 0.0, -0.08698248863220215, 0.0], "object_id": null, "class": null}, {"question": "Where is the checkers board?", "answer": "Entryway table", "category": "object localization", "question_id": "2f6546fe-af9d-4986-bbf6-3c189353126a", "episode_history": "00889-HMkoS756sz6", "extra_answers": ["on the table near the font door", "on the table with two chairs", "On the table in the corner of the living room.", "On the circular table by the window. "], "position": [2.309299945831299, 2.7712581157684326, -4.815240859985352], "rotation": [0.9962098598480225, 0.0, -0.08698248863220215, 0.0], "object_id": null, "class": null}, {"question": "What is between the kitchen and the open closet?", "answer": "An empty room", "category": "spatial understanding", "question_id": "a8cc7ee8-36ea-4726-bb0e-2642ffc2c2d0", "episode_history": "00897-LEFTm3JecaC", "position": [-6.145190238952637, 0.13376569747924805, 4.191065788269043], "rotation": [-0.*****************, 0.0, 0.886599063873291, 0.0], "object_id": null, "class": null}, {"question": "Is there anything on top of the cardboard box", "answer": "no", "category": "object state recognition", "question_id": "e36087a0-f638-4769-8055-dc357e706c71", "episode_history": "00897-LEFTm3JecaC", "position": [-6.145190238952637, 0.13376569747924805, 4.191065788269043], "rotation": [-0.*****************, 0.0, 0.886599063873291, 0.0], "object_id": null, "class": null}, {"question": "What material are most of the cabinets of the kitchen made of?", "answer": "<PERSON>", "category": "attribute recognition", "question_id": "e3f6ebae-2b21-4356-856b-52a54fc45b60", "episode_history": "00846-LNg5mXe1BDj", "position": [-4.315883159637451, 0.009984016418457031, 5.392625331878662], "rotation": [-0.04903780296444893, 0.0, 0.9987969994544983, 0.0], "object_id": null, "class": null}, {"question": "If you were to go down the stairs, then straight into the tv room, turn left in the door and go straight until you enter the last room, which room would it be?", "answer": "The restroom", "category": "spatial understanding", "question_id": "6faa9052-c5ae-44b9-a024-ab14474d0c29", "episode_history": "00846-LNg5mXe1BDj", "position": [-4.315883159637451, 0.009984016418457031, 5.392625331878662], "rotation": [-0.04903780296444893, 0.0, 0.9987969994544983, 0.0], "object_id": null, "class": null}, {"question": "In which room is the stationary bike?", "answer": "In the TV room", "category": "object localization", "question_id": "de9bd341-0754-4c6c-9558-c973832c3942", "episode_history": "00846-LNg5mXe1BDj", "extra_answers": ["in the room with the television", "in the room with the television and recliner", "In the room with the recliner", "In the first basement room"], "position": [-4.315883159637451, 0.009984016418457031, 5.392625331878662], "rotation": [-0.04903780296444893, 0.0, 0.9987969994544983, 0.0], "object_id": null, "class": null}, {"question": "Which color is the dinning table?", "answer": "<PERSON>", "category": "attribute recognition", "question_id": "ba31f08f-0721-4773-b3da-fdeef9dad06f", "episode_history": "00839-zt1RVoi7PcG", "position": [0.6585885286331177, 0.11293554306030273, -0.6034373641014099], "rotation": [0.13774478435516357, 0.0, 0.9904677271842957, 0.0], "object_id": null, "class": null}, {"question": "Is there a place where I can leave my pet? If so where is it?", "answer": "Yes, a pet cage in the living room", "category": "functional reasoning", "question_id": "2b7089df-2398-43e7-9262-1c2a8069c524", "episode_history": "00839-zt1RVoi7PcG", "position": [0.6585885286331177, 0.11293554306030273, -0.6034373641014099], "rotation": [0.13774478435516357, 0.0, 0.9904677271842957, 0.0], "object_id": null, "class": null}, {"question": "Theres a frame about a video game above the brown couch on the top floor, which video game is it?", "answer": "Super Mario", "category": "world knowledge", "question_id": "49723897-3ce8-4944-80ac-35f430386b4f", "episode_history": "00839-zt1RVoi7PcG", "position": [0.6585885286331177, 0.11293554306030273, -0.6034373641014099], "rotation": [0.13774478435516357, 0.0, 0.9904677271842957, 0.0], "object_id": null, "class": null}, {"question": "There are ornaments about a festivity near the chimney, about which festivity are they?", "answer": "Halloween", "category": "world knowledge", "question_id": "e6f70056-2a9c-429c-9570-f136d2eb4120", "episode_history": "00839-zt1RVoi7PcG", "position": [0.6585885286331177, 0.11293554306030273, -0.6034373641014099], "rotation": [0.13774478435516357, 0.0, 0.9904677271842957, 0.0], "object_id": null, "class": null}, {"question": "Which room is the TV in?", "answer": "Living room", "category": "object localization", "question_id": "da17ae0d-58f0-4099-8bd6-4537e67d93f9", "episode_history": "00839-zt1RVoi7PcG", "extra_answers": ["The living room.", "The room with the large grey couch. ", "The room with the fireplace", "The room with the pumpkin and halloween decorations"], "position": [0.6585885286331177, 0.11293554306030273, -0.6034373641014099], "rotation": [0.13774478435516357, 0.0, 0.9904677271842957, 0.0], "object_id": null, "class": null}, {"question": "What's to the left of the toilet?", "answer": "<PERSON><PERSON><PERSON>", "category": "object recognition", "question_id": "6e2f5803-5dca-4853-85d5-468e8f27ce89", "episode_history": "00858-cYkrGrCg2kB", "position": [8.70068645477295, -2.996455669403076, -0.8739748001098633], "rotation": [-0.9437316656112671, 0.0, 0.3307121992111206, 0.0], "object_id": null, "class": null}, {"question": "Does the bathroom door open \"into\" or \"out\" of the bathroom", "answer": "Out", "category": "object state recognition", "question_id": "6767409d-f832-4f59-87d1-2dfc3c66d343", "episode_history": "00858-cYkrGrCg2kB", "position": [8.70068645477295, -2.996455669403076, -0.8739748001098633], "rotation": [-0.9437316656112671, 0.0, 0.3307121992111206, 0.0], "object_id": null, "class": null}, {"question": "Where is the baby high chair?", "answer": "Next to the entrance to the TV room", "category": "object localization", "question_id": "66648ca6-3619-4e93-98bb-f4606a842144", "episode_history": "00812-mma8eWq3nNQ", "extra_answers": ["next to the kitchen counter", "next to the door between the kitchen and the living room", "to the right of the kitchen counter top", "Across the dining table"], "position": [5.743940353393555, 0.22690463066101074, 2.5308737754821777], "rotation": [0.20290705561637878, 0.0, 0.9791980385780334, 0.0], "object_id": null, "class": null}, {"question": "What size mattresses are in the house", "answer": "Full", "category": "attribute recognition", "question_id": "961fa9de-6a12-49e7-8e69-2590b96242af", "episode_history": "00870-AYpsNQsWncn", "position": [1.8659064769744873, 0.1274430751800537, 3.8206887245178223], "rotation": [0.3319377899169922, 0.0, 0.943301260471344, 0.0], "object_id": null, "class": null}, {"question": "Are the doors open or closed?", "answer": "Open", "category": "object state recognition", "question_id": "e25996f5-9a95-4b55-a357-a71d65acede3", "episode_history": "00870-AYpsNQsWncn", "position": [1.8659064769744873, 0.1274430751800537, 3.8206887245178223], "rotation": [0.3319377899169922, 0.0, 0.943301260471344, 0.0], "object_id": null, "class": null}, {"question": "Where can I use the bathroom?", "answer": "I did not see a bathroom", "category": "world knowledge", "question_id": "d16c927b-8d75-4743-8018-97320c76b351", "episode_history": "00870-AYpsNQsWncn", "position": [1.8659064769744873, 0.1274430751800537, 3.8206887245178223], "rotation": [0.3319377899169922, 0.0, 0.943301260471344, 0.0], "object_id": null, "class": null}, {"question": "What is on the bed with the blue sheet", "answer": "Construction insulation", "category": "object localization", "question_id": "a8c02803-de6a-4dd8-97b1-98301dbda075", "episode_history": "00870-AYpsNQsWncn", "extra_answers": ["Insulation", "Insulation", "Some boxes", "Some cardboard material"], "position": [1.8659064769744873, 0.1274430751800537, 3.8206887245178223], "rotation": [0.3319377899169922, 0.0, 0.943301260471344, 0.0], "object_id": null, "class": null}, {"question": "What is in the basket by the front door?", "answer": "an umbrella", "category": "object recognition", "question_id": "7d868374-5434-40f3-a95d-66548d092d6d", "episode_history": "00821-eF36g7L6Z9M", "position": [3.546989917755127, -3.****************, 3.103692054748535], "rotation": [-0.773204505443573, 0.0, 0.6341568231582642, 0.0], "object_id": null, "class": null}, {"question": "Is the AC vent on the stairs open or closed?", "answer": "open", "category": "object state recognition", "question_id": "15d330b7-11bd-4b29-8263-5235cab34c21", "episode_history": "00821-eF36g7L6Z9M", "position": [3.546989917755127, -3.****************, 3.103692054748535], "rotation": [-0.773204505443573, 0.0, 0.6341568231582642, 0.0], "object_id": null, "class": null}, {"question": "What is in the corner of the dining room?", "answer": "a plant", "category": "object recognition", "question_id": "3321cf87-c5fe-46cc-90c2-33d114503de6", "episode_history": "00809-Qpor2mEya8F", "position": [11.057259559631348, 0.10094380378723145, 0.853672444820404], "rotation": [0.9420677423477173, 0.0, 0.3354228138923645, 0.0], "object_id": null, "class": null}, {"question": "Is there space for another pillow on the back of the bench with the coat hangers?", "answer": "No", "category": "spatial understanding", "question_id": "1fcfa31c-43d6-4c9a-acb6-21f019956e1c", "episode_history": "00809-Qpor2mEya8F", "position": [11.057259559631348, 0.10094380378723145, 0.853672444820404], "rotation": [0.9420677423477173, 0.0, 0.3354228138923645, 0.0], "object_id": null, "class": null}, {"question": "Is the downstairs closet door open or closed?", "answer": "closed", "category": "object state recognition", "question_id": "fa7906e8-12fb-4511-9b6d-9b514a3e63f9", "episode_history": "00809-Qpor2mEya8F", "position": [11.057259559631348, 0.10094380378723145, 0.853672444820404], "rotation": [0.9420677423477173, 0.0, 0.3354228138923645, 0.0], "object_id": null, "class": null}, {"question": "Are the blinds closed?", "answer": "No", "category": "object state recognition", "question_id": "d5c19ea7-5931-4501-a3cf-bed0eb161a9f", "episode_history": "00867-uLz9jNga3kC", "position": [-6.192185878753662, 0.15472698211669922, 3.244832992553711], "rotation": [-0.7260233163833618, 0.0, 0.6876700520515442, 0.0], "object_id": null, "class": null}, {"question": "Where is the plaid blanket?", "answer": "On the back of the couch", "category": "object localization", "question_id": "0bc41aa3-c14f-4117-92ff-868fda0e5e4b", "episode_history": "00867-uLz9jNga3kC", "extra_answers": ["On the reclining chair near the window in the living room.", "On the back of one of the reclining leather chairs.", "on the couch in the bottom floor close to the television", "on the couch in the bottom floor"], "position": [-6.192185878753662, 0.15472698211669922, 3.244832992553711], "rotation": [-0.7260233163833618, 0.0, 0.6876700520515442, 0.0], "object_id": null, "class": null}, {"question": "Where is the three blade fan?", "answer": "Above the stairs", "category": "object localization", "question_id": "27fd907f-7c89-4e0a-9c6c-73ba570b0df6", "episode_history": "00847-bCPU9suPUw9", "extra_answers": ["on the ceiling right above the stairs", "on the top floor right above the stairds", "To the left of the bathroom", "Near the stairs"], "position": [7.132144451141357, 2.880223035812378, -0.9273329377174377], "rotation": [-0.26747167110443115, 0.0, 0.9635657668113708, 0.0], "object_id": null, "class": null}, {"question": "Where is the four blade fan?", "answer": "In the living room", "category": "object localization", "question_id": "cd8dd632-4431-44b9-9cbb-6eec2317344c", "episode_history": "00847-bCPU9suPUw9", "extra_answers": ["on the ceiling in the living room", "on the ceiling in the living room above the sofa", "By the couches", "Near the patio doors"], "position": [7.132144451141357, 2.880223035812378, -0.9273329377174377], "rotation": [-0.26747167110443115, 0.0, 0.9635657668113708, 0.0], "object_id": null, "class": null}, {"question": "What is the blue object left of the downstairs bed?", "answer": "a humidifier", "category": "object recognition", "question_id": "2d2cc029-bad4-4dd3-9dc6-aeceb0207e2a", "episode_history": "00844-q5QZSEeHe5g", "position": [1.1950371265411377, 0.15387678146362305, -7.093991279602051], "rotation": [-0.5859194397926331, 0.0, 0.8103693127632141, 0.0], "object_id": null, "class": null}, {"question": "what color are the dining room walls?", "answer": "brown", "category": "attribute recognition", "question_id": "8de58b75-8369-4185-b39f-82838fc29d87", "episode_history": "00844-q5QZSEeHe5g", "position": [1.1950371265411377, 0.15387678146362305, -7.093991279602051], "rotation": [-0.5859194397926331, 0.0, 0.8103693127632141, 0.0], "object_id": null, "class": null}, {"question": "What color is the front door?", "answer": "white", "category": "attribute recognition", "question_id": "b41c3183-c6cb-4bc6-a554-13e27532b2ad", "episode_history": "00844-q5QZSEeHe5g", "position": [1.1950371265411377, 0.15387678146362305, -7.093991279602051], "rotation": [-0.5859194397926331, 0.0, 0.8103693127632141, 0.0], "object_id": null, "class": null}, {"question": "Where can I workout?", "answer": "upstairs bedroom", "category": "functional reasoning", "question_id": "f739f880-79fc-4066-9ca1-b04943433974", "episode_history": "00844-q5QZSEeHe5g", "position": [1.1950371265411377, 0.15387678146362305, -7.093991279602051], "rotation": [-0.5859194397926331, 0.0, 0.8103693127632141, 0.0], "object_id": null, "class": null}]