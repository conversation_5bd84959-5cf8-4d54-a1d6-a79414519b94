[{"question": "What is above the wooden table in the living room?", "answer": "A blue, white, and orange painting", "category": "object recognition", "question_id": "a605c40f-96e7-4bec-a1cb-6d48e88e39cd", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "What color are the pillows in the kitchen?", "answer": "Blue", "category": "attribute recognition", "question_id": "3a5be057-47d2-4f78-98a9-729ef19b3d8b", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "What color is the front door?", "answer": "<PERSON>", "category": "attribute recognition", "question_id": "30dc765d-80c3-4901-9c69-65e6b48e254a", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "What is to the left of the frontdoor?", "answer": "A storage closet", "category": "spatial understanding", "question_id": "f5a17a09-ce4b-4123-bf40-d2239cf38cb8", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "What is hanging from the oven handle?", "answer": "A towel", "category": "spatial understanding", "question_id": "00c2be2a-1377-4fae-a889-30936b7890c3", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "Is the light above the sink turned on?", "answer": "Yes", "category": "object state recognition", "question_id": "4dbd213e-56cd-481a-8ff5-ed9a8d636dbc", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "Where can I warm up?", "answer": "Next to the fireplace", "category": "functional reasoning", "question_id": "6d132959-fd48-4fef-a736-4e5853849547", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "Where can I get recipes for what to cook?", "answer": "The open cookbook on the kitchen counter top", "category": "functional reasoning", "question_id": "ae19adeb-498a-4814-b955-e0af05623f9b", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "What is shown on the painting above the couch?", "answer": "Horses", "category": "world knowledge", "question_id": "cbffc0cd-04aa-4686-97bf-887c0dc840bd", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "Where can I get drinking water in the kitchen?", "answer": "From water dispenser in the fridge", "category": "world knowledge", "question_id": "4cc4212e-0db2-421f-8bb5-93817e58f9b4", "episode_history": "00824-Dd4bFSTQ8gi", "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "Where is the microwave?", "answer": "Above the stovetop", "category": "object localization", "question_id": "7ebac357-a338-4ce0-975a-62141e90a3c3", "episode_history": "00824-Dd4bFSTQ8gi", "extra_answers": ["Over the stove.", "Over the open cookbook.", "above the stove top", "above the book right next to the stove"], "position": [6.892691135406494, 0.06882420182228088, 1.4872137308120728], "rotation": [0.37141478061676025, 0.0, 0.9284670948982239, 0.0], "object_id": null, "class": null}, {"question": "What is behind the armchair in the living room?", "answer": "A dog bed", "category": "object recognition", "question_id": "e6fb0c2e-5f92-4835-ba38-6af958b7a1d3", "episode_history": "00848-ziup5kvtCCR", "position": [0.26384106278419495, 0.021223172545433044, 7.149479389190674], "rotation": [0.9547726511955261, 0.0, 0.29733675718307495, 0.0], "object_id": null, "class": null}, {"question": "What is hanging on the wall in the living room?", "answer": "A clock", "category": "object recognition", "question_id": "a36ab369-6f78-4311-a943-b6862cd28b55", "episode_history": "00848-ziup5kvtCCR", "position": [0.26384106278419495, 0.021223172545433044, 7.149479389190674], "rotation": [0.9547726511955261, 0.0, 0.29733675718307495, 0.0], "object_id": null, "class": null}, {"question": "What type of numbers are on the clock?", "answer": "Roman numerals", "category": "attribute recognition", "question_id": "0df60236-15ad-4166-a31a-a98d14214fdb", "episode_history": "00848-ziup5kvtCCR", "position": [0.26384106278419495, 0.021223172545433044, 7.149479389190674], "rotation": [0.9547726511955261, 0.0, 0.29733675718307495, 0.0], "object_id": null, "class": null}, {"question": "Where is the teddy bear?", "answer": "In the dog bed in the living room.", "category": "object localization", "question_id": "01fcc568-f51e-4e12-b976-5dc8d554135a", "episode_history": "00848-ziup5kvtCCR", "extra_answers": ["on the baby mat next to the tv", "on the ground near the tv", "On the blue bed in the living room", "On the blue object on the floor to the right of the telvision"], "position": [0.26384106278419495, 0.021223172545433044, 7.149479389190674], "rotation": [0.9547726511955261, 0.0, 0.29733675718307495, 0.0], "object_id": null, "class": null}, {"question": "Where is the wreath?", "answer": "Hanging on the front door.", "category": "object localization", "question_id": "6852b358-4820-471d-9263-d32ef0cecd0b", "episode_history": "00848-ziup5kvtCCR", "extra_answers": ["on the front door", "through the glass of the front door", "Below the brown blinds on the front door", "In the center of the window on the front door"], "position": [0.26384106278419495, 0.021223172545433044, 7.149479389190674], "rotation": [0.9547726511955261, 0.0, 0.29733675718307495, 0.0], "object_id": null, "class": null}, {"question": "What is the grey item on the bed?", "answer": "A blanket", "category": "object recognition", "question_id": "48d8aa7f-61cb-469b-9b6d-2549d1210281", "episode_history": "00876-mv2HUxq3B53", "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "What color is the countertop on the porch?", "answer": "Black", "category": "attribute recognition", "question_id": "45a5e082-a9e9-47ca-a036-dfafba92b16c", "episode_history": "00876-mv2HUxq3B53", "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "What material are the stools in the kitchen made out of?", "answer": "Leather", "category": "attribute recognition", "question_id": "013bb857-f47d-4b50-add4-023cc4ff414c", "episode_history": "00876-mv2HUxq3B53", "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "Where do I reach for laundry detergent?", "answer": "For the shelf above the washing machine.", "category": "spatial understanding", "question_id": "ba5f1c9b-9a41-4a84-829b-f9b8ccd19b69", "episode_history": "00876-mv2HUxq3B53", "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "Is the toilet seat open or closed?", "answer": "Closed.", "category": "object state recognition", "question_id": "f17869a2-2a4d-4ce4-b262-cb69618e3394", "episode_history": "00876-mv2HUxq3B53", "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "Is the television turned on or off?", "answer": "On", "category": "object state recognition", "question_id": "e0d20472-8fa6-4e8d-880d-22d4eed3fbb8", "episode_history": "00876-mv2HUxq3B53", "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "I want to check my outfit for a dinner party, how can I do this?", "answer": "Using the large mirror outside the first bedroom.", "category": "functional reasoning", "question_id": "fc9d2a18-6197-4c8b-abd8-be0c493e5450", "episode_history": "00876-mv2HUxq3B53", "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "I want to host a dinner party on a summer evening, where should I set the table?", "answer": "On the table on the porch.", "category": "world knowledge", "question_id": "1b36e675-74ff-46ad-8caa-c33da46a5a67", "episode_history": "00876-mv2HUxq3B53", "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "Where is the round mirror?", "answer": "On the wall above the head of the bed in the first bedroom.", "category": "object localization", "question_id": "d3742804-8363-4346-a622-5bcaeffb25e9", "episode_history": "00876-mv2HUxq3B53", "extra_answers": ["above the bed in the first bedroom", "in the first bedroom", "on the wall above the bed in the first bedroom", "on the wall above the pillows on the bed in the first bedroom"], "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "Where is the ceiling fan?", "answer": "Above the bed in the second bedroom", "category": "object localization", "question_id": "dfdc3b36-d98f-42a7-b2ea-dceb4af1794a", "episode_history": "00876-mv2HUxq3B53", "extra_answers": ["in the bedroom with a patio", "in the bedroom with a twin bed", "above the bed in the second bedroom with the television", "above the bed in the second bedroom"], "position": [-8.009016036987305, 0.05035419762134552, 8.276389122009277], "rotation": [-0.9870320558547974, 0.0, 0.16052371263504028, 0.0], "object_id": null, "class": null}, {"question": "what is kept on the tray in the kitchen counter?", "answer": "banana", "category": "object recognition", "question_id": "f2063c53-72d8-4cd8-b2cb-78ceee86449d", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "what color are the numbers written on the wall clock?", "answer": "white", "category": "attribute recognition", "question_id": "c1b2ccf5-b56d-4ced-9cec-eaf62fedc675", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "what color are the chairs?", "answer": "brown", "category": "attribute recognition", "question_id": "de038605-c441-4a30-968b-7815bad3a3c9", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "what is between the fruit bowl and knife set?", "answer": "a container of spices", "category": "spatial understanding", "question_id": "a5c5bb29-700a-4ef5-b17d-aaa47bb0ef3f", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "is the table in the living room completely clean?", "answer": "no", "category": "spatial understanding", "question_id": "b05e7b30-6a4d-4381-9d05-a42ed0c90e30", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "is the television on or off?", "answer": "off", "category": "object state recognition", "question_id": "bd5e9e4e-c6be-40e9-a923-fcc6aa321947", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "is the fan on or off?", "answer": "off", "category": "object state recognition", "question_id": "d8183087-f3dd-47c1-b985-733923edc4a0", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "where can I keep a photo frame?", "answer": "on the counter below the television with other frames", "category": "functional reasoning", "question_id": "109eaad4-6e68-4da1-8f98-a0d8589ec26d", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "where can I keep a couple of apples?", "answer": "on the fruit basket in the kitchen counter.", "category": "functional reasoning", "question_id": "67cd7145-4b1f-4b2a-a698-8e4738cb7c41", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "how can I clean my hands?", "answer": "there is a sink in the kitchen", "category": "world knowledge", "question_id": "d4c10718-fd57-4db0-93c1-b54deb4b1b25", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "where can I keep a new knife I got?", "answer": "there is a knife holder in the kitchen counter next to the gas stove.", "category": "world knowledge", "question_id": "1dcdd225-eba2-4ba1-97b6-c4cdc7ca4e9b", "episode_history": "00880-Nfvxx8J5NCo", "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "where is the paper roll?", "answer": "In the kitchen counter right next to the gas stove", "category": "object localization", "question_id": "b93ea8d4-4b9a-46a3-b9b4-3d79c5ce074e", "episode_history": "00880-Nfvxx8J5NCo", "extra_answers": ["next to the stove", "to the right of the stove", "to the right of the stove", "on the counter across the sink"], "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "where is the clock?", "answer": "in the living room hallway next to the television", "category": "object localization", "question_id": "90ab6389-d85e-42ad-b44a-af4849da2631", "episode_history": "00880-Nfvxx8J5NCo", "extra_answers": ["on the wall across from the kitchen", "on the wall across from the fridge", "hallway before the living room", "the wall cross the kitchen"], "position": [-9.55214786529541, 0.18085697293281555, -0.3554811477661133], "rotation": [0.10294225811958313, 0.0, 0.9946873188018799, 0.0], "object_id": null, "class": null}, {"question": "What can be seen through the window in the living room?", "answer": "Trees and hills", "category": "object recognition", "question_id": "911693d9-2d28-4ff2-83a9-c67b83753831", "episode_history": "00835-q3zU7Yy5E5s", "position": [-0.4913436472415924, 0.03521956503391266, -0.4102398455142975], "rotation": [0.661293089389801, 0.0, 0.7501276135444641, 0.0], "object_id": null, "class": null}, {"question": "Where is the fireplace?", "answer": "In the living room, to the right of the radiator and window", "category": "object localization", "question_id": "9b2d06e5-ca78-4519-a9ca-75c06209b770", "episode_history": "00835-q3zU7Yy5E5s", "extra_answers": ["In the corner of the living room.", "Next to the blue couch.", "in the living room ", "in the living room right next to the window"], "position": [-0.4913436472415924, 0.03521956503391266, -0.4102398455142975], "rotation": [0.661293089389801, 0.0, 0.7501276135444641, 0.0], "object_id": null, "class": null}]